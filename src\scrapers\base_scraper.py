from abc import ABC
import asyncio
from random import randint
import threading
from venv import logger
import requests
from bs4 import BeautifulSoup
from PIL import Image
import io
import os
from openai import OpenAI
from dotenv import load_dotenv

def get_base_dir():
    return os.path.dirname(os.path.abspath(__file__))

env_path = os.path.join(get_base_dir(), ".env")
if os.path.exists(env_path):
    load_dotenv(dotenv_path=env_path)
client = OpenAI(api_key=os.getenv("OPENROUTER_API_KEY"))

class HeaderManager:
    def __init__(self):
        self.api_key = os.getenv("SCRAPE_OPS_API_KEY")
        self.headers_list = self.get_headers_list()

    def get_headers_list(self):
        """Obtém lista de headers da API ScrapeOps"""
        print(f"[HeaderManager] Iniciando método get_headers_list")
        
        if not self.api_key:
            print(f"[HeaderManager] Erro no método get_headers_list: API key não encontrada")
            return []
            
        try:
            print(f"[HeaderManager] Fazendo requisição para ScrapeOps API")
            response = requests.get(
                url='https://headers.scrapeops.io/v1/browser-headers',
                params={
                    'api_key': self.api_key,
                    'num_results': '50'
                },
                timeout=10  # Adiciona timeout
            )
            
            if response.status_code != 200:
                print(f"[HeaderManager] Erro no método get_headers_list: Status code {response.status_code}")
                return []
                
            json_response = response.json()
            headers_list = json_response.get("result", [])
            
            print(f"[HeaderManager] Headers obtidos com sucesso. Total: {len(headers_list)}")
            return headers_list
            
        except requests.exceptions.Timeout:
            print(f"[HeaderManager] Erro no método get_headers_list: Timeout na requisição")
            return []
        except requests.exceptions.RequestException as e:
            print(f"[HeaderManager] Erro no método get_headers_list: Erro na requisição - {str(e)}")
            return []
        except ValueError as e:
            print(f"[HeaderManager] Erro no método get_headers_list: Erro ao processar JSON - {str(e)}")
            return []
        except Exception as e:
            print(f"[HeaderManager] Erro no método get_headers_list: Erro inesperado - {str(e)}")
            return []

    def get_random_header(self):
        """Retorna um header aleatório da lista"""
        print(f"[HeaderManager] Iniciando método get_random_header")
        
        if not self.headers_list:
            print(f"[HeaderManager] Aviso no método get_random_header: Lista de headers vazia, usando header padrão")
            return {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }
        
        try:
            random_index = randint(0, len(self.headers_list) - 1)
            header = self.headers_list[random_index]
            print(f"[HeaderManager] Header aleatório selecionado com sucesso")
            return header
        except Exception as e:
            print(f"[HeaderManager] Erro no método get_random_header: {str(e)}")
            return {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

class BaseScraper(ABC):
    NEW_SIZE = (852, 850)

    def __init__(self):
        self._header_manager = None
        self._is_running = False
        self._status = ""
        self._current_loop = None
        self.header_manager = HeaderManager()
        self.session = requests.Session()

    def get_header_manager(self):
        if self._header_manager is None:
            self._header_manager = HeaderManager()
        return self._header_manager

    def get_soup(self, url):
        headers = self.get_header_manager().get_random_header()
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return BeautifulSoup(response.text, "html.parser")
        return None

    def resize_image(self, url_image: str) -> str:
        try:
            response = requests.get(url_image)
            response.raise_for_status()
            with Image.open(io.BytesIO(response.content)) as img:
                img = img.resize(self.NEW_SIZE, Image.LANCZOS)
                output = io.BytesIO()
                img.save(output, format="JPEG")
                output.seek(0)
                return output.getvalue()
        except Exception as e:
            print(f"Error resizing image: {e}")
            return url_image

    def stop(self):
        """Para o scraper"""
        if not self._is_running:
            return False

        self._is_running = False
        self.set_status("Parando scraper")

        # Força a parada de todas as requisições pendentes
        if hasattr(self, 'ai_manager'):
            if self.ai_manager.current_api == "OpenRouter":
                asyncio.create_task(self.ai_manager.cleanup())

        # Fecha a sessão atual
        if hasattr(self, 'session'):
            self.session.close()

        # Cancela todas as tarefas assíncronas
        if self._current_loop and self._current_loop.is_running():
            for task in asyncio.all_tasks(self._current_loop):
                task.cancel()

        return True

    def start(self, log_callback=None):
        """Inicia o scraper"""
        if self._is_running:
            return False

        self._is_running = True
        self.set_status("Iniciando scraper")

        def run_scraper():
            try:
                self._current_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._current_loop)
                self._current_loop.run_until_complete(self.run())
            except Exception as e:
                logger.error(f"Erro durante execução do scraper: {e}")
            finally:
                self._is_running = False
                if self._current_loop:
                    self._current_loop.close()
                    self._current_loop = None

        thread = threading.Thread(target=run_scraper)
        thread.daemon = True
        thread.start()
        return True

