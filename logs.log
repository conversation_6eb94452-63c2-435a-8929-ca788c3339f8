ontent=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=155, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=155)], thoughts_token_count=444, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=603, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:39:48,500 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:39:48,500 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:39:48,500 - utils.ai_manager - INFO - Obtendo subcategoria para: Granplus Ração Para Gatos Castrados Gran Plus Frango E Arroz 10 1Kg (categoria: Melhor Amigo)
2025-05-28 13:39:48,500 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:39:48,504 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:39:50,041 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:39:50,041 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:39:50,041 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=137, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=137)], thoughts_token_count=236, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=375, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:39:50,041 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:39:50,041 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:39:50,041 - utils.ai_manager - INFO - Obtendo categoria para: Ração Hill's Science Diet Indoor para Gatos Adultos Castrados sabor frango - 3.17kg
2025-05-28 13:39:50,041 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:39:50,041 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:39:53,082 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:39:53,088 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:39:53,088 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=158, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=158)], thoughts_token_count=636, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=798, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:39:53,092 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:39:53,093 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:39:53,094 - utils.ai_manager - INFO - Obtendo subcategoria para: Ração Hill's Science Diet Indoor para Gatos Adultos Castrados sabor frango - 3.17kg (categoria: Melhor Amigo)
2025-05-28 13:39:53,095 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal
2025-05-28 13:39:53,101 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:39:54,087 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:39:54,087 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:39:54,087 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=140, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=140)], thoughts_token_count=106, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=248, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:39:54,087 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:39:54,087 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:39:54,087 - utils.ai_manager - INFO - Obtendo categoria para: Ração Baw Waw para gatos adultos sabor Mix - 900g
2025-05-28 13:39:54,092 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:39:54,092 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:39:56,099 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:39:56,099 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:39:56,099 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=149, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=149)], thoughts_token_count=402, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=555, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:39:56,099 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:39:56,099 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:39:56,099 - utils.ai_manager - INFO - Obtendo subcategoria para: Ração Baw Waw para gatos adultos sabor Mix - 900g (categoria: Melhor Amigo)
2025-05-28 13:39:56,099 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:39:56,099 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:39:58,589 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:39:58,597 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:39:58,597 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=131, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=131)], thoughts_token_count=537, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=670, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:39:58,597 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:39:58,597 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:39:58,602 - utils.ai_manager - INFO - Obtendo categoria para: Ração Baw Waw Natural Pro para gatos filhotes sabor Carne e Arroz - 1kg
2025-05-28 13:39:58,602 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:39:58,603 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:01,321 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:01,362 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:01,362 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=154, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=154)], thoughts_token_count=502, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=660, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:01,374 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:01,374 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:01,374 - utils.ai_manager - INFO - Obtendo subcategoria para: Ração Baw Waw Natural Pro para gatos filhotes sabor Carne e Arroz - 1kg (categoria: Melhor Amigo)
2025-05-28 13:40:01,374 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:01,375 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:03,227 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:03,354 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:03,357 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=136, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=136)], thoughts_token_count=285, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=423, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:03,440 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:03,440 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:03,440 - utils.ai_manager - INFO - Obtendo categoria para: Pack Nestlé Purina Friskies Ração Úmida Para Gatos Adultos Salmão Ao Molho - Com 15 Sachês 85g
2025-05-28 13:40:03,440 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:03,440 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:05,767 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:05,769 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:05,769 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=168, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=168)], thoughts_token_count=386, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=558, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:05,771 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:05,772 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:05,772 - utils.ai_manager - INFO - Obtendo subcategoria para: Pack Nestlé Purina Friskies Ração Úmida Para Gatos Adultos Salmão Ao Molho - Com 15 Sachês 85g (categoria: Melhor Amigo)
2025-05-28 13:40:05,772 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:05,774 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:07,725 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:07,727 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:07,727 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=150, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=150)], thoughts_token_count=357, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=509, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:07,728 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:07,728 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:07,729 - utils.ai_manager - INFO - Obtendo categoria para: PURINA® FRISKIES® Megamix Castrados 3kg Grts 300g (Bônus Bag)
2025-05-28 13:40:07,729 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:07,729 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:10,703 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:10,719 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:10,719 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=161, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=161)], thoughts_token_count=534, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=699, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:10,719 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:10,719 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:10,719 - utils.ai_manager - INFO - Obtendo subcategoria para: PURINA® FRISKIES® Megamix Castrados 3kg Grts 300g (Bônus Bag) (categoria: Melhor Amigo)
2025-05-28 13:40:10,719 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:10,719 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:13,415 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:13,415 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:13,415 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=143, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=143)], thoughts_token_count=561, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=706, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:13,415 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:13,415 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:13,415 - utils.ai_manager - INFO - Obtendo categoria para: Ração para Gatos Castrados Gran Plus Salmão e Arroz 3Kg
2025-05-28 13:40:13,415 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:13,415 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:15,988 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:15,988 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:15,988 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=151, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=151)], thoughts_token_count=475, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=630, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:15,988 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:15,988 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:15,988 - utils.ai_manager - INFO - Obtendo subcategoria para: Ração para Gatos Castrados Gran Plus Salmão e Arroz 3Kg (categoria: Melhor Amigo)
2025-05-28 13:40:15,988 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:15,988 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:17,275 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:17,275 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:17,275 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=133, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=133)], thoughts_token_count=179, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=314, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:17,275 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:17,275 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:17,275 - utils.ai_manager - INFO - Obtendo categoria para: Purina Cat Chow Ração Nestlé Para Gatos Castrados Sabor Frango - 10 1Kg Purina - Sabor Frango
2025-05-28 13:40:17,275 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:17,275 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:19,312 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:19,312 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:19,312 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=164, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=164)], thoughts_token_count=398, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=566, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:19,312 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:19,312 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:19,312 - utils.ai_manager - INFO - Obtendo subcategoria para: Purina Cat Chow Ração Nestlé Para Gatos Castrados Sabor Frango - 10 1Kg Purina - Sabor Frango (categoria: Melhor Amigo)
2025-05-28 13:40:19,312 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:19,312 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:20,831 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:20,831 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:20,831 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=146, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=146)], thoughts_token_count=191, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=339, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:20,831 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:20,831 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:20,831 - utils.ai_manager - INFO - Obtendo categoria para: ROYAL CANIN Ração Royal Canin Exigent Gatos Adultos 1 5Kg Royal Canin Raça Adulto
2025-05-28 13:40:20,831 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:20,831 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:24,180 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:24,180 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:24,180 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=159, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=159)], thoughts_token_count=598, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=761, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:24,180 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:24,180 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:24,180 - utils.ai_manager - INFO - Obtendo subcategoria para: ROYAL CANIN Ração Royal Canin Exigent Gatos Adultos 1 5Kg Royal Canin Raça Adulto (categoria: Melhor Amigo)
2025-05-28 13:40:24,180 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:24,180 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:26,432 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:26,447 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:26,447 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=141, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=141)], thoughts_token_count=397, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=540, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:26,447 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:26,447 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:26,447 - utils.ai_manager - INFO - Obtendo categoria para: Granplus Ração Granplus Gatos Adultos Frango E Arroz 10 1Kg
2025-05-28 13:40:26,447 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:26,447 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:28,272 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:28,272 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:28,282 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=154, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=154)], thoughts_token_count=306, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=464, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:28,282 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:28,282 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:28,282 - utils.ai_manager - INFO - Obtendo subcategoria para: Granplus Ração Granplus Gatos Adultos Frango E Arroz 10 1Kg (categoria: Melhor Amigo)
2025-05-28 13:40:28,282 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:28,286 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:29,794 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:29,794 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:29,794 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=136, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=136)], thoughts_token_count=229, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=367, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:29,794 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:29,794 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:29,794 - utils.ai_manager - INFO - Obtendo categoria para: SPECIAL CAT Ração Special Cat Premium Peixe Adultos 10 1Kg
2025-05-28 13:40:29,794 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:29,794 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:31,890 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:31,890 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:31,890 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=150, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=150)], thoughts_token_count=404, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=558, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:31,890 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:31,890 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:31,890 - utils.ai_manager - INFO - Obtendo subcategoria para: SPECIAL CAT Ração Special Cat Premium Peixe Adultos 10 1Kg (categoria: Melhor Amigo)
2025-05-28 13:40:31,890 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:31,890 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:34,750 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:34,750 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:34,750 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=132, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=132)], thoughts_token_count=538, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=672, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:34,750 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:34,750 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:34,750 - utils.ai_manager - INFO - Obtendo categoria para: Pack Ração Úmida Sheba Sachê para Gatos Adultos Sabor Frango Assado 85g - 40 unidades
2025-05-28 13:40:34,750 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:34,750 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:37,015 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:37,015 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:37,015 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=163, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=163)], thoughts_token_count=410, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=577, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:37,015 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:37,015 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:37,015 - utils.ai_manager - INFO - Obtendo subcategoria para: Pack Ração Úmida Sheba Sachê para Gatos Adultos Sabor Frango Assado 85g - 40 unidades (categoria: Melhor Amigo)
2025-05-28 13:40:37,015 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:37,015 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:38,389 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:38,389 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:38,389 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=145, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=145)], thoughts_token_count=205, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=352, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:38,389 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:38,389 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:38,389 - utils.ai_manager - INFO - Obtendo categoria para: Pack Ração Úmida Sheba Sachê para Gatos Adultos Sabor Atum Marinado 85g - 40 unidades
2025-05-28 13:40:38,389 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:38,389 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:40,871 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:40,871 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:40,871 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=163, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=163)], thoughts_token_count=368, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=535, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:40,886 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:40,886 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:40,886 - utils.ai_manager - INFO - Obtendo subcategoria para: Pack Ração Úmida Sheba Sachê para Gatos Adultos Sabor Atum Marinado 85g - 40 unidades (categoria: Melhor Amigo)
2025-05-28 13:40:40,886 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:40,886 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:42,379 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:42,379 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:42,379 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=145, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=145)], thoughts_token_count=234, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=381, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:42,379 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:42,379 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:42,379 - utils.ai_manager - INFO - Obtendo categoria para: Pack Ração Úmida Whiskas Sachê Peixe Jelly para Gatos Adultos 85 g - 40 unidades
2025-05-28 13:40:42,379 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:42,379 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:44,397 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:44,397 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:44,397 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=161, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=161)], thoughts_token_count=372, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=537, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:44,397 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:44,397 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:44,397 - utils.ai_manager - INFO - Obtendo subcategoria para: Pack Ração Úmida Whiskas Sachê Peixe Jelly para Gatos Adultos 85 g - 40 unidades (categoria: Melhor Amigo)
2025-05-28 13:40:44,397 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:44,397 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:45,722 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:45,722 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:45,722 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=143, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=143)], thoughts_token_count=212, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=357, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:45,722 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:45,722 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:45,722 - utils.ai_manager - INFO - Obtendo categoria para: Pack Ração Úmida Whiskas Sachê Frango ao Molho para Gatos Adultos 85 g - 20 unidades
2025-05-28 13:40:45,722 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:40:45,722 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:48,078 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:48,078 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:48,078 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=163, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=163)], thoughts_token_count=451, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=618, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:48,094 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:48,094 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:40:48,094 - utils.ai_manager - INFO - Obtendo subcategoria para: Pack Ração Úmida Whiskas Sachê Frango ao Molho para Gatos Adultos 85 g - 20 unidades (categoria: Melhor Amigo)
2025-05-28 13:40:48,094 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:40:48,094 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:40:50,290 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:40:50,290 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:40:50,290 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Rações')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=2, candidates_tokens_details=None, prompt_token_count=145, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=145)], thoughts_token_count=327, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=474, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:40:50,290 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:40:50,290 - utils.ai_manager - INFO - Subcategoria obtida: Rações, ID: 1
2025-05-28 13:40:50,290 - utils.amazon_integration - INFO - Processando 111 produtos para salvar em CSV        
2025-05-28 13:40:50,313 - utils.amazon_integration - INFO - Salvando 111 produtos para a categoria 'Amazon_Produtos' em C:\TRABALHO\PROMOBELL - LTDA\promohunter\output\Amazon_Produtos.csv
2025-05-28 13:40:50,326 - utils.amazon_integration - INFO - Produtos da categoria 'Amazon_Produtos' salvos com sucesso.
2025-05-28 13:40:50,342 - utils.amazon_integration - INFO - Processamento da Amazon concluído. Total de produtos encontrados: 111