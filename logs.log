t_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=5, candidates_tokens_details=None, prompt_token_count=140, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=140)], thoughts_token_count=275, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=420, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:24:13,995 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:24:13,995 - utils.ai_manager - INFO - Subcategoria obtida: Coleiras e Guias, ID: 4
2025-05-28 13:24:13,995 - utils.ai_manager - INFO - Obtendo categoria para: Coleira de Cachorro Retrátil Dupla para 2 Cães Grandes (até 50 Libras cada), 360 ° de Rotação de Cabeça Dupla de 9,84 Pés de Absorção de Choque (Cinza)
2025-05-28 13:24:13,995 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:24:13,995 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:24:16,102 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:24:16,102 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:24:16,102 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=190, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=190)], thoughts_token_count=370, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=564, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:24:16,102 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:24:16,102 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:24:16,102 - utils.ai_manager - INFO - Obtendo subcategoria para: Coleira de Cachorro Retrátil Dupla para 2 Cães Grandes (até 50 Libras cada), 360 ° de Rotação de Cabeça Dupla de 9,84 Pés de Absorção de Choque (Cinza) (categoria: Melhor Amigo)
2025-05-28 13:24:16,102 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:24:16,102 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:24:17,932 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:24:17,932 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:24:17,932 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Coleiras e Guias')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=5, candidates_tokens_details=None, prompt_token_count=172, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=172)], thoughts_token_count=292, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=469, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:24:17,932 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:24:17,948 - utils.ai_manager - INFO - Subcategoria obtida: Coleiras e Guias, ID: 4
2025-05-28 13:24:17,948 - utils.ai_manager - INFO - Obtendo categoria para: Coleira Retrátil de Cachorro, 9,84 Pés/3 Metros de Comprimento, Com Luz LED, Coleira de Animais de Estimação, para Cães Médios de até 30 Kg       
2025-05-28 13:24:17,948 - utils.ai_manager - INFO - Tentativa 1/3 para _get_category_googleAI_internal
2025-05-28 13:24:17,948 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:24:21,067 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:24:21,067 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:24:21,067 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Melhor Amigo')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=4, candidates_tokens_details=None, prompt_token_count=183, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=183)], thoughts_token_count=617, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=804, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:24:21,067 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:24:21,082 - utils.ai_manager - INFO - Categoria obtida: Melhor Amigo, ID: 15
2025-05-28 13:24:21,082 - utils.ai_manager - INFO - Obtendo subcategoria para: Coleira Retrátil de Cachorro, 9,84 Pés/3 Metros de Comprimento, Com Luz LED, Coleira de Animais de Estimação, para Cães Médios de até 30 Kg (categoria: Melhor Amigo)
2025-05-28 13:24:21,082 - utils.ai_manager - INFO - Tentativa 1/3 para _get_subcategory_googleAI_internal      
2025-05-28 13:24:21,083 - google_genai.models - INFO - AFC is enabled with max remote calls: 10.
2025-05-28 13:24:23,054 - httpx - INFO - HTTP Request: POST https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-04-17:generateContent "HTTP/1.1 200 OK"
2025-05-28 13:24:23,070 - google_genai.models - INFO - AFC remote call 1 is done.
2025-05-28 13:24:23,070 - utils.ai_manager - INFO - Resposta completa da API Google AI: candidates=[Candidate(content=Content(parts=[Part(video_metadata=None, thought=None, inline_data=None, code_execution_result=None, executable_code=None, file_data=None, function_call=None, function_response=None, text='Coleiras e Guias')], role='model'), citation_metadata=None, finish_message=None, token_count=None, finish_reason=<FinishReason.STOP: 'STOP'>, url_context_metadata=None, avg_logprobs=None, grounding_metadata=None, index=0, logprobs_result=None, safety_ratings=None)] create_time=None response_id=None model_version='models/gemini-2.5-flash-preview-04-17' prompt_feedback=None usage_metadata=GenerateContentResponseUsageMetadata(cache_tokens_details=None, cached_content_token_count=None, candidates_token_count=5, candidates_tokens_details=None, prompt_token_count=165, prompt_tokens_details=[ModalityTokenCount(modality=<MediaModality.TEXT: 'TEXT'>, token_count=165)], thoughts_token_count=393, tool_use_prompt_token_count=None, tool_use_prompt_tokens_details=None, total_token_count=563, traffic_type=None) automatic_function_calling_history=[] parsed=None
2025-05-28 13:24:23,070 - utils.ai_manager - INFO - Requisição bem-sucedida na tentativa 1
2025-05-28 13:24:23,070 - utils.ai_manager - INFO - Subcategoria obtida: Coleiras e Guias, ID: 4
2025-05-28 13:24:23,070 - utils.amazon_integration - INFO - Processando 57 produtos para salvar em CSV
2025-05-28 13:24:23,091 - utils.amazon_integration - ERROR - Erro ao executar scraper da Amazon: cannot access local variable 'amazon_category_name' where it is not associated with a value
