import tkinter as tk
from tkinter import ttk

class ErrorDialog:
    def __init__(self, parent, title, message):
        self.parent = parent
        self.title = title
        self.message = message
        
        # Criar janela de diálogo
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x200")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Centralizar a janela
        self.center_window()
        
        # Configurar o layout
        self.setup_ui()
    
    def center_window(self):
        """Centraliza a janela de diálogo em relação à janela pai"""
        self.dialog.update_idletasks()
        
        # Obter dimensões e posição da janela pai
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        parent_x = self.parent.winfo_x()
        parent_y = self.parent.winfo_y()
        
        # Calcular posição central
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def setup_ui(self):
        """Configura os elementos da interface"""
        # Frame principal
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Ícone de erro
        error_label = ttk.Label(main_frame, text="❌", font=("TkDefaultFont", 24))
        error_label.pack(pady=(0, 10))
        
        # Mensagem de erro
        message_label = ttk.Label(
            main_frame, 
            text=self.message,
            wraplength=350,
            justify=tk.CENTER
        )
        message_label.pack(pady=(0, 20))
        
        # Botão OK
        ok_button = ttk.Button(
            main_frame,
            text="OK",
            command=self.dialog.destroy,
            width=10
        )
        ok_button.pack()
        
        # Bind Enter key to close dialog
        self.dialog.bind("<Return>", lambda e: self.dialog.destroy())
        
        # Focar no botão OK
        ok_button.focus_set()
    
    def show(self):
        """Exibe o diálogo e aguarda até ser fechado"""
        self.dialog.wait_window()