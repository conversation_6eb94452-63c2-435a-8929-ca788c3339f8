import time
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from datetime import datetime
import threading
from utils.test_scraper import test_scraper_fields
from utils.ai_manager import AIManager
from utils.store_manager import StoreManager
from utils.amazon_integration import AmazonIntegration

class ExecutionFrame(ttk.Frame):
    def __init__(self, master, scraper_process, category_manager, ai_manager=None, store_manager=None, status_callback=None):
        super().__init__(master, padding="10")
        self.scraper_process = scraper_process
        self.category_manager = category_manager
        self.status_callback = status_callback

        # Configurar codificação padrão para UTF-8
        import sys
        if sys.stdout.encoding != 'utf-8':
            sys.stdout.reconfigure(encoding='utf-8')

        # Usa a instância de AIManager recebida ou cria uma nova
        self.ai_manager = ai_manager if isinstance(ai_manager, AIManager) else AIManager()

        # Usa a instância de StoreManager recebida ou cria uma nova
        self.store_manager = store_manager if store_manager else StoreManager()
        # Inicializa o integrador da Amazon, passando o ai_manager
        self.amazon_integration = AmazonIntegration(
            store_manager=self.store_manager,
            ai_manager=self.ai_manager
        )

        self.create_widgets()


    def on_api_change(self, event=None):
        """Handler para mudança na seleção da API"""
        selected_api = self.api_var.get()
        self.ai_manager.set_api(selected_api)
        self.add_log(f"API alterada para: {selected_api}")
        self.focus_set()

    def on_store_change(self, event=None):
        """Handler para mudança na seleção da loja"""
        selected_store = self.store_var.get()
        self.store_manager.set_current_store(selected_store)
        self.add_log(f"Loja alterada para: {selected_store}")

        if selected_store == "Magalu":
            self.magalu_options_frame.pack(fill=tk.X, pady=5)
            self.full_delivery_check.pack(side="left", padx=(0, 10))
            self.old_price_check.pack(side="left")
        elif selected_store == "Amazon":
            self.magalu_options_frame.pack(fill=tk.X, pady=5)
            self.full_delivery_check.pack_forget() 
            self.old_price_check.pack(side="left")             
        else:
            self.magalu_options_frame.pack_forget()

        self.focus_set()

    def on_full_delivery_change(self):
        """Handler para mudança no checkbox de entrega Full"""
        full_delivery = self.full_delivery_var.get()
        # Configurar o scraper para filtrar produtos com entrega Full
        if hasattr(self.scraper_process, 'scraper') and self.scraper_process.scraper:
            self.scraper_process.scraper.set_only_full_delivery(full_delivery)
            self.add_log(f"Filtro de entrega Full {'ativado' if full_delivery else 'desativado'}")

    def on_old_price_change(self):
        """Handler para mudança no checkbox de preço antigo"""
        old_price = self.old_price_var.get()
        # Configurar o scraper para filtrar produtos com preço antigo
        if hasattr(self.scraper_process, 'scraper') and self.scraper_process.scraper:
            self.scraper_process.scraper.set_only_with_old_price(old_price)
            self.add_log(f"Filtro de produtos com desconto {'ativado' if old_price else 'desativado'}")

    def create_widgets(self):
        # Frame de configurações
        config_frame = ttk.LabelFrame(self, text="Configurações")
        config_frame.pack(fill=tk.X, pady=5)

        # Container para configurações
        config_container = ttk.Frame(config_frame)
        config_container.pack(padx=5, pady=5, fill=tk.X)

        # Seleção de loja
        store_container = ttk.Frame(config_container)
        store_container.pack(fill=tk.X, pady=5)
        ttk.Label(store_container, text="Selecione a Loja:").pack(side="left", padx=(0, 5))

        self.store_var = tk.StringVar(value=self.store_manager.get_current_store())
        self.store_dropdown = ttk.Combobox(
            store_container,
            textvariable=self.store_var,
            values=self.store_manager.get_store_names(),
            state="readonly",
            width=20
        )
        self.store_dropdown.pack(side="left")
        self.store_dropdown.bind("<<ComboboxSelected>>", self.on_store_change)

        # Seleção de API
        api_container = ttk.Frame(config_container)
        api_container.pack(fill=tk.X, pady=5)
        ttk.Label(api_container, text="Selecione a API:").pack(side="left", padx=(0, 5))

        self.api_var = tk.StringVar(value="OpenRouter")
        self.api_dropdown = ttk.Combobox(
            api_container,
            textvariable=self.api_var,
            values=["OpenRouter", "GoogleAI", "OpenAI"],
            state="readonly",
            width=20
        )
        self.api_dropdown.pack(side="left")
        self.api_dropdown.bind("<<ComboboxSelected>>", self.on_api_change)

        # Opções específicas para Magalu
        self.magalu_options_frame = ttk.Frame(config_container)

        # Checkbox para filtrar produtos com entrega Full (apenas para Magalu)
        self.full_delivery_var = tk.BooleanVar(value=True)
        self.full_delivery_check = ttk.Checkbutton(
            self.magalu_options_frame,
            text="Entrega Full",
            variable=self.full_delivery_var,
            command=self.on_full_delivery_change
        )

        # Checkbox para filtrar produtos com preço antigo (apenas para Magalu)
        self.old_price_var = tk.BooleanVar(value=True)
        self.old_price_check = ttk.Checkbutton(
            self.magalu_options_frame,
            text="Apenas produtos com desconto",
            variable=self.old_price_var,
            command=self.on_old_price_change
        )

        # Mostrar opções do Magalu apenas se a loja atual for Magalu
        if self.store_manager.get_current_store() == "Magalu":
            self.magalu_options_frame.pack(fill=tk.X, pady=5)
            self.full_delivery_check.pack(side="left", padx=(0, 10))
            self.old_price_check.pack(side="left")

        # Botões de controle
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, pady=5)

        self.run_button = ttk.Button(control_frame, text="Executar Scraper",
                                    command=self.toggle_execution)
        self.run_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(control_frame, text="Testar Scraper",
                  command=self.test_scraper).pack(side=tk.LEFT, padx=5)

        # Status
        self.status_label = ttk.Label(self, text="Pronto para execução")
        self.status_label.pack(pady=10, anchor=tk.W)

        # Frame de log com botões de limpar e copiar
        log_frame = ttk.LabelFrame(self, text="Log de Execução")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Frame para os botões de controle
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.pack(fill=tk.X, padx=5, pady=2)

        # Botão de copiar com ícone
        self.copy_button = ttk.Button(
            log_control_frame,
            text="📋",
            width=3,
            command=self.copy_logs
        )
        self.copy_button.pack(side=tk.RIGHT, padx=5)

        # Botão de limpar
        ttk.Button(
            log_control_frame,
            text="Limpar Logs",
            command=self.clear_logs
        ).pack(side=tk.RIGHT, padx=5)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.log_text.config(state=tk.DISABLED)

        # Configurar fonte para suportar caracteres especiais
        try:
            self.log_text.configure(font=('Consolas', 10))
        except:
            pass  # Se a fonte não estiver disponível, usa a fonte padrão

    def remove_dropdown_focus(self, event, dropdown):
        """Remove o foco do dropdown quando clicar em qualquer lugar da janela"""
        if event.widget != dropdown:
            self.focus_set()  # Define o foco para o frame principal

    def clear_logs(self):
        """Limpa todos os logs da área de texto"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)  # Inicialmente não editável

    def add_log(self, log_entry):
        """Adiciona entrada de log formatada"""
        try:
            timestamp = datetime.now().strftime("[%d/%m/%Y %H:%M:%S]")

            # Remove timestamp duplicado se existir
            if '] INFO:' in log_entry or '] ERROR:' in log_entry or '] WARNING:' in log_entry:
                log_entry = log_entry.split(': ', 1)[1] if ': ' in log_entry else log_entry

            formatted_entry = f"{timestamp} {log_entry}\n"

            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, formatted_entry)
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)

            # Atualizar interface
            self.update()
        except Exception as e:
            print(f"Erro ao adicionar log: {str(e)}")

    def test_scraper(self):
        """Testa se o scraper está funcionando corretamente"""
        # Verificar se há categorias
        categories = self.category_manager.get_all_categories()
        if not categories:
            messagebox.showerror("Erro", "Não há categorias para testar")
            return

        self.add_log("Iniciando teste do scraper...")

        # Pegar a primeira categoria
        first_category = categories[0]  # Assumindo que é um dicionário
        self.add_log(f"Usando primeira categoria: {first_category['name']}")

        # Iniciar teste em thread separada para não travar a interface
        threading.Thread(target=self._run_test, args=(first_category,), daemon=True).start()

    def _run_test(self, category):
        """Executa o teste de campos em thread separada"""
        url_template = category['url_template']
        name = category['name']

        self.add_log(f"Testando campos do scraper em: {url_template}")

        try:
            # Obter a loja atual
            current_store = self.store_manager.get_current_store()
            self.add_log(f"Loja atual: {current_store}")

            # Usar função de teste do scraper com callback para logs
            def log_callback(message):
                self.add_log(message)

            result, message = test_scraper_fields(url_template, log_callback, store_name=current_store)

            if result:
                self.add_log("✅ Teste concluído com sucesso! Todos os campos estão funcionando.")
                self.add_log(f"Detalhes: {message}")
            else:
                self.add_log("❌ Falha no teste do scraper!")
                self.add_log(f"Problema: {message}")
        except Exception as e:
            self.add_log(f"❌ Erro durante o teste: {str(e)}")

    def toggle_execution(self):
        selected_store = self.store_var.get()

        if self.scraper_process.is_running() or (selected_store == "Amazon" and self.amazon_integration.scraper._is_running):
            if selected_store == "Amazon":
                self.amazon_integration.stop()
            else:
                self.stop_execution()

        else:
            if selected_store == "Amazon":
                threading.Thread(target=self.run_amazon_scraper, daemon=True).start()
            else:
                self.start_execution()
    def start_execution(self):
        categories = self.category_manager.get_all_categories()
        if not categories:
            messagebox.showerror("Erro", "Não há categorias para processar")
            return

        self.add_log("Iniciando processamento...")
        self.add_log(f"Categorias encontradas: {len(categories)}")

        # Teste rápido antes de iniciar o processo completo
        self.add_log("Executando teste rápido antes de iniciar o scraper completo...")

        # Testar o scraper primeiro com a primeira categoria
        first_category = categories[0]
        threading.Thread(target=self._start_after_test, args=(first_category,), daemon=True).start()

    def _start_after_test(self, category):
        """Inicia execução após teste bem-sucedido"""
        try:
            url_template = category['url_template']
            name = category['name']

            if not url_template.startswith(('http://', 'https://')):
                self.add_log("❌ URL inválida. Certifique-se de que a URL começa com http:// ou https://")
                return

            # Obter a loja atual
            current_store = self.store_manager.get_current_store()
            self.add_log(f"Loja atual: {current_store}")

            # Passar o nome da loja para o teste
            result, message = test_scraper_fields(url_template, log_callback=self.add_log, store_name=current_store)

            if result:
                self.add_log("✅ Teste inicial bem-sucedido! Iniciando scraper completo...")

                # Configurar callback para logs do processo
                def process_callback(message):
                    self.add_log(message)

                # Configurar filtros do Magalu se a loja atual for Magalu
                magalu_filters = None
                if self.store_manager.get_current_store() == "Magalu":
                    magalu_filters = {
                        'only_with_old_price': self.old_price_var.get(),
                        'only_full_delivery': self.full_delivery_var.get()
                    }
                    self.add_log(f"Aplicando filtros do Magalu: apenas com desconto={magalu_filters['only_with_old_price']}, apenas com entrega Full={magalu_filters['only_full_delivery']}")
                elif self.store_manager.get_current_store() == "Amazon":
                    amazon_filters = {
                        'only_with_old_price': self.old_price_var.get(),
                    }
                    self.add_log(f"Aplicando filtros do Amazon: apenas com desconto={amazon_filters['only_with_old_price']}, apenas com entrega Full={amazon_filters['only_full_delivery']}")
                # Modificar o ScraperProcess para processar os dados com AI
                if self.scraper_process.start(log_callback=process_callback, magalu_filters=magalu_filters, amazon_filters=amazon_filters):
                    self.run_button.config(text="Parar Scraper")
                    self.status_label.config(text="Scraper em execução...")
                    if self.status_callback:
                        self.status_callback("Scraper em execução...")

                    # Iniciar thread para monitorar o processo
                    threading.Thread(target=self._monitor_process, daemon=True).start()
                else:
                    self.add_log("❌ Falha ao iniciar o scraper!")
            else:
                self.add_log(f"❌ Teste falhou. Não é possível iniciar o scraper: {message}")
        except Exception as e:
            self.add_log(f"❌ Erro durante o teste inicial: {str(e)}")

    def _monitor_process(self):
        """Monitora o processo do scraper e atualiza o log"""
        try:
            while self.scraper_process and self.scraper_process.is_running():
                try:
                    status = self.scraper_process.get_status()
                    if status:
                        if isinstance(status, bytes):
                            status = status.decode('utf-8', errors='replace')
                        self.add_log(f"Status: {status}")

                        # Atualizar interface
                        self.status_label.config(text=status)
                        if self.status_callback:
                            self.status_callback(status)
                except Exception as e:
                    self.add_log(f"Erro ao processar status: {str(e)}")

                # Verificar a cada 2 segundos
                time.sleep(2)

            # Processo terminou
            self.add_log("Scraper finalizado!")
            self.run_button.config(text="Executar Scraper")
            self.status_label.config(text="Scraper interrompido")
            if self.status_callback:
                self.status_callback("Scraper interrompido")

        except Exception as e:
            self.add_log(f"Erro no monitoramento: {str(e)}")
            # Garantir que a UI seja atualizada mesmo em caso de erro
            self.run_button.config(text="Executar Scraper")
            self.status_label.config(text="Scraper interrompido")
            if self.status_callback:
                self.status_callback("Scraper interrompido")

    def stop_execution(self):
        if self.scraper_process.stop():
            self.add_log("Solicitação para interromper o scraper enviada...")
            self.run_button.config(text="Executar Scraper")
            self.status_label.config(text="Interrompendo scraper...")
            if self.status_callback:
                self.status_callback("Interrompendo scraper...")
        else:
            messagebox.showerror("Erro", "Falha ao interromper o scraper")

    def copy_logs(self):
        """Copia o conteúdo dos logs para a área de transferência"""
        if self.log_text.get(1.0, tk.END).strip():
            self.clipboard_clear()
            self.clipboard_append(self.log_text.get(1.0, tk.END))

            # Mudar ícone para check
            self.copy_button.configure(text="✓")

            # Agendar retorno do ícone original após 3 segundos
            self.after(3000, lambda: self.copy_button.configure(text="📋"))
