Collecting openai
  Downloading openai-1.66.0-py3-none-any.whl.metadata (25 kB)
Collecting anyio<5,>=3.5.0 (from openai)
  Using cached anyio-4.8.0-py3-none-any.whl.metadata (4.6 kB)
Collecting distro<2,>=1.7.0 (from openai)
  Using cached distro-1.9.0-py3-none-any.whl.metadata (6.8 kB)
Collecting httpx<1,>=0.23.0 (from openai)
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting jiter<1,>=0.4.0 (from openai)
  Using cached jiter-0.9.0-cp312-cp312-win_amd64.whl.metadata (5.3 kB)
Collecting pydantic<3,>=1.9.0 (from openai)
  Using cached pydantic-2.10.6-py3-none-any.whl.metadata (30 kB)
Collecting sniffio (from openai)
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting tqdm>4 (from openai)
  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)
Requirement already satisfied: typing-extensions<5,>=4.11 in c:\trabalho\promobell\promohunter\.venv\lib\site-packages (from openai) (4.12.2)
Requirement already satisfied: idna>=2.8 in c:\trabalho\promobell\promohunter\.venv\lib\site-packages (from anyio<5,>=3.5.0->openai) (3.10)
Requirement already satisfied: certifi in c:\trabalho\promobell\promohunter\.venv\lib\site-packages (from httpx<1,>=0.23.0->openai) (2025.1.31)
Collecting httpcore==1.* (from httpx<1,>=0.23.0->openai)
  Using cached httpcore-1.0.7-py3-none-any.whl.metadata (21 kB)
Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->openai)
  Using cached h11-0.14.0-py3-none-any.whl.metadata (8.2 kB)
Collecting annotated-types>=0.6.0 (from pydantic<3,>=1.9.0->openai)
  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)
Collecting pydantic-core==2.27.2 (from pydantic<3,>=1.9.0->openai)
  Using cached pydantic_core-2.27.2-cp312-cp312-win_amd64.whl.metadata (6.7 kB)
Collecting colorama (from tqdm>4->openai)
  Using cached colorama-0.4.6-py2.py3-none-any.whl.metadata (17 kB)
Downloading openai-1.66.0-py3-none-any.whl (566 kB)
   ---------------------------------------- 566.4/566.4 kB 6.5 MB/s eta 0:00:00
Using cached anyio-4.8.0-py3-none-any.whl (96 kB)
Using cached distro-1.9.0-py3-none-any.whl (20 kB)
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Using cached httpcore-1.0.7-py3-none-any.whl (78 kB)
Using cached jiter-0.9.0-cp312-cp312-win_amd64.whl (207 kB)
Using cached pydantic-2.10.6-py3-none-any.whl (431 kB)
Using cached pydantic_core-2.27.2-cp312-cp312-win_amd64.whl (2.0 MB)
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Using cached tqdm-4.67.1-py3-none-any.whl (78 kB)
Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)
Using cached colorama-0.4.6-py2.py3-none-any.whl (25 kB)
Using cached h11-0.14.0-py3-none-any.whl (58 kB)
Installing collected packages: sniffio, pydantic-core, jiter, h11, distro, colorama, annotated-types, tqdm, pydantic, httpcore, anyio, httpx, openai
Successfully installed annotated-types-0.7.0 anyio-4.8.0 colorama-0.4.6 distro-1.9.0 h11-0.14.0 httpcore-1.0.7 httpx-0.28.1 jiter-0.9.0 openai-1.66.0 pydantic-2.10.6 pydantic-core-2.27.2 sniffio-1.3.1 tqdm-4.67.1
