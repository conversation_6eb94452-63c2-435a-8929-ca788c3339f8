import os
import sys
import json
import logging
import time
from bs4 import BeautifulSoup
import requests
import threading
import re
import asyncio
from utils.header_manager import HeaderManager

# Configurar encoding padrão para UTF-8
if sys.stdout.encoding != 'utf-8':
    sys.stdout.reconfigure(encoding='utf-8')
if sys.stderr.encoding != 'utf-8':
    sys.stderr.reconfigure(encoding='utf-8')

# Adiciona o diretório raiz ao PYTHONPATH
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from utils.selector_manager import SelectorManager
from utils.ai_manager import AIManager
from utils.store_manager import StoreManager

logger = logging.getLogger(__name__)

class Scraper:
    def __init__(self, store_manager=None):
        logger.info("Inicializando Scraper")
        self.categories_file = "categories.json"
        self.output_dir = "output"
        self.session = requests.Session()
        self.store_manager = store_manager if store_manager else StoreManager()
        self.selector_manager = SelectorManager(store_manager=self.store_manager)
        self.ai_manager = AIManager()
        self.header_manager = HeaderManager()
        self.ensure_output_dir()
        self._is_running = False
        self.current_status = "Não iniciado"
        self.lock = threading.Lock()
        self.loop = None

        # Configurações para filtros do Magalu
        self.only_with_old_price = True  # Filtrar apenas produtos com preço antigo
        self.only_full_delivery = False  # Filtrar apenas produtos com entrega Full

        # Configurações para filtros da Amazon
        self.amazon_only_discount = True  # Filtrar apenas produtos com desconto



    def start(self, log_callback=None):
        """Inicia o scraper"""
        if self._is_running:
            return False

        self._is_running = True
        self.set_status("Iniciando scraper")

        def run_scraper():
            try:
                # Criar e configurar o loop de eventos
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # Executar a corrotina run()
                loop.run_until_complete(self.run())

            except Exception as e:
                logger.error(f"Erro durante execução do scraper: {e}")
            finally:
                self._is_running = False
                loop.close()

        # Iniciar em uma thread separada
        thread = threading.Thread(target=run_scraper)
        thread.daemon = True
        thread.start()
        return True


    def stop(self):
        """Para o scraper"""
        if not self._is_running:
            return False

        logger.info("Iniciando processo de parada do scraper")
        self._is_running = False
        self.set_status("Parando scraper")

        # Fecha a sessão atual
        try:
            self.session.close()
            logger.info("Sessão HTTP fechada")
        except Exception as e:
            logger.error(f"Erro ao fechar sessão HTTP: {e}")

        # Força a interrupção do loop de eventos
        try:
            if self.loop and self.loop.is_running():
                for task in asyncio.all_tasks(self.loop):
                    task.cancel()
                logger.info("Tarefas assíncronas canceladas")
        except Exception as e:
            logger.error(f"Erro ao cancelar tarefas assíncronas: {e}")

        logger.info("Scraper parado com sucesso")
        return True

    async def test_category(self, category):
        """Testa se é possível extrair produtos de uma categoria"""
        try:
            url_template = category['url_template']

            # Formatar URL de acordo com a loja atual
            current_store = self.store_manager.get_current_store()

            if current_store == "MercadoLivre":
                # Formato do Mercado Livre
                url = url_template.format(i=1)
            elif current_store == "Magalu":
                # Formato da Magalu
                if "?" in url_template:
                    # Se já tem parâmetros, adiciona a página
                    url = f"{url_template}&page=1"
                else:
                    # Se não tem parâmetros, adiciona a página
                    url = f"{url_template}?page=1"
            elif current_store == "Amazon":
                # Formato da Amazon (usa índice de início)
                url = url_template.format(i=0)
            else:
                # Formato genérico
                url = url_template.format(i=1)

            logger.info(f"Testando URL: {url}")
            headers = self.header_manager.get_random_header()

            # Garantir que há um User-Agent válido
            if not headers.get('User-Agent'):
                headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

            logger.info(f"Headers sendo usados: {headers}")

            response = self.session.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            if response.status_code == 200:
                # Salvar o HTML para depuração
                self._save_debug_html(response.text, url)

                soup = BeautifulSoup(response.text, 'html.parser')

                # Obtém seletores ativos para produtos
                product_selectors = self.get_active_selectors('product')
                logger.info(f"Seletores ativos carregados: {product_selectors}")

                # Tenta cada seletor até encontrar produtos
                products = []
                for selector in product_selectors:
                    logger.info(f"Testando seletor: {selector}")
                    products = soup.select(selector)
                    logger.info(f"Seletor {selector} encontrou {len(products)} produtos")
                    if products:
                        logger.info(f"Produtos encontrados usando seletor: {selector}")
                        return True, f"Encontrados {len(products)} produtos com o seletor: {selector}"

                # Se nenhum seletor funcionou, vamos investigar o HTML
                logger.warning("Nenhum seletor funcionou. Investigando HTML...")
                logger.info(f"Tamanho do HTML: {len(response.text)} caracteres")
                logger.info(f"Primeiros 500 caracteres: {response.text[:500]}")

                # Verificar se há divs genéricos
                all_divs = soup.select('div')
                logger.info(f"Total de divs encontradas: {len(all_divs)}")

                # Procurar por classes que contenham 'card' ou 'product'
                potential_products = []
                for div in all_divs[:20]:  # Verificar apenas os primeiros 20
                    classes = div.get('class', [])
                    if classes:
                        class_str = ' '.join(classes)
                        if any(keyword in class_str.lower() for keyword in ['card', 'product', 'item', 'result']):
                            potential_products.append(class_str)

                if potential_products:
                    logger.info(f"Possíveis produtos encontrados: {potential_products[:5]}")
                else:
                    logger.warning("Nenhuma div com classes relevantes encontrada")

                # Verificar se o problema é de encoding
                try:
                    # Tentar decodificar com brotli se necessário
                    import brotli
                    if 'br' in response.headers.get('content-encoding', ''):
                        logger.info("Conteúdo comprimido com Brotli detectado")
                        decompressed = brotli.decompress(response.content)
                        soup = BeautifulSoup(decompressed.decode('utf-8'), 'html.parser')

                        # Tentar novamente com conteúdo descomprimido
                        for selector in product_selectors:
                            products = soup.select(selector)
                            if products:
                                logger.info(f"Produtos encontrados após descompressão Brotli: {len(products)} com seletor: {selector}")
                                return True, f"Encontrados {len(products)} produtos com o seletor: {selector}"
                except Exception as e:
                    logger.warning(f"Erro ao tentar descompressão Brotli: {e}")

                # Se não encontrou produtos, tenta seletores específicos para a loja atual
                current_store = self.store_manager.get_current_store()

                if current_store == "MercadoLivre":
                    generic_selectors = [
                        'div.andes-card.poly-card--grid-card',
                        'div.andes-card',
                        'div.ui-search-result',
                        'li.ui-search-layout__item',
                        'div.promotion-item'
                    ]
                elif current_store == "Magalu":
                    generic_selectors = [
                        'div.product-card',
                        'div.product-item',
                        'li.product-list-item',
                        'div.productCard',
                        'div.sc-dPiLbb',
                        'div.sc-hKwDye',
                        'a[data-testid="product-card-container"]',
                        'div[data-testid="product-card"]',
                        'div.sc-jrQzAO',
                        'li.neemu-product-list',
                        'div.nm-product-item',
                        'div.product-li',
                        'div.showcase-product',
                        'div.product-grid-item',
                        'div.product-box',
                        'div.product-container',
                        'div.product-wrapper'
                    ]
                elif current_store == "Amazon":
                    generic_selectors = [
                        'div[data-testid][data-asin]',
                        'div.ProductCard-module__card_uyr_Jh7WpSkPx4iEpn4w',
                        'div.GridItem-module__container_PW2gdkwTj1GQzdwJjejN',
                        'div.a-section.a-spacing-base',
                        'div[data-asin]',
                        'div.s-result-item',
                        'div.a-section.a-spacing-small',
                        'div.product-card',
                        'div.deal-card'
                    ]
                else:
                    generic_selectors = [
                        'div.product-card', 'div.product-item', 'li.product-list-item',
                        'div.productCard', 'div.sc-dPiLbb', 'div.sc-hKwDye',
                        'a[data-testid="product-card-container"]', 'div.sc-jrQzAO',
                        'div.ui-search-result', 'li.ui-search-layout__item'
                    ]

                for selector in generic_selectors:
                    products = soup.select(selector)
                    if products:
                        logger.info(f"Produtos encontrados usando seletor genérico: {selector}")
                        # Adicionar este seletor à lista de seletores ativos
                        current_store = self.store_manager.get_current_store()
                        self.selector_manager.add_selector('product', selector, f'Seletor para produtos do {current_store} (auto)')
                        return True, f"Encontrados {len(products)} produtos com o seletor genérico: {selector}"

                return False, "Não foi possível encontrar produtos na página"
            else:
                return False, f"Erro ao acessar a página: {response.status_code}"

        except Exception as e:
            logger.error(f"Erro ao testar categoria: {e}")
            return False, f"Erro ao testar categoria: {str(e)}"

    async def run(self):
        """Executa o scraper para todas as categorias"""
        try:
            categories = self.load_categories()
            if not categories:
                self.set_status("Nenhuma categoria encontrada")
                logger.error("Nenhuma categoria encontrada")
                return

            self.set_status(f"Iniciando processamento de {len(categories)} categorias")

            # Executar teste rápido antes de iniciar o scraper completo
            self.set_status("Executando teste rápido antes de iniciar o scraper completo...")

            # Testar a primeira categoria
            if categories:
                success, message = await self.test_category(categories[0])
                if not success:
                    self.set_status(f"[ERRO] Teste falhou. Não é possível iniciar o scraper: {message}")
                    logger.error(f"Teste falhou: {message}")
                    return
                else:
                    self.set_status(f"✅ Teste bem-sucedido: {message}")
                    logger.info(f"Teste bem-sucedido: {message}")

            for category in categories:
                if not self._is_running:
                    break

                try:
                    await self.process_category(category)
                except Exception as e:
                    logger.error(f"Erro ao processar categoria {category}: {e}")
                    continue

            self.set_status("Processamento concluído")

        except Exception as e:
            self.set_status(f"Erro: {str(e)}")
            logger.error(f"Erro durante execução: {e}")
        finally:
            self._is_running = False
    def get_status(self):
        """Retorna o status atual do scraper"""
        try:
            with self.lock:
                return self.current_status
        except Exception as e:
            logger.error(f"Erro ao obter status: {e}")
            return "Status indisponível"


    def set_status(self, status):
        """Atualiza o status do scraper"""
        with self.lock:
            self.current_status = status
            logger.info(f"Status: {status}")

    def is_running(self):
        """Verifica se o scraper está em execução"""
        return self._is_running

    def set_only_with_old_price(self, value):
        """Define se deve filtrar apenas produtos com preço antigo"""
        self.only_with_old_price = value
        logger.info(f"Filtro de produtos com preço antigo: {value}")

    def set_only_full_delivery(self, value):
        """Define se deve filtrar apenas produtos com entrega Full"""
        self.only_full_delivery = value
        logger.info(f"Filtro de produtos com entrega Full: {value}")

    def check_full_delivery_in_page(self, soup, url):
        """Verifica se a página contém produtos com entrega Full"""
        try:
            # Verificar se a página contém a palavra "Full" em elementos relevantes
            full_elements = soup.select('[data-testid="productCard-shipping-tag"], .full-tag, span.full-badge')
            if full_elements:
                for elem in full_elements:
                    text = elem.get_text().strip()
                    if "Full" in text:
                        logger.info(f"Página contém produtos com entrega Full: {url}")
                        return True

            # Verificar no HTML da página
            html_content = str(soup)
            if "Full" in html_content and ("entrega" in html_content.lower() or "frete" in html_content.lower()):
                logger.info(f"Página contém referências a entrega Full: {url}")
                return True

            # Verificar elementos SVG
            svg_elements = soup.select('svg use')
            for svg_elem in svg_elements:
                href_value = svg_elem.get('xlink:href')
                if href_value and "#FastDeliveryIcon" in href_value:
                    logger.info(f"Página contém ícones de entrega Full: {url}")
                    return True

            logger.info(f"Página não contém produtos com entrega Full: {url}")
            return False
        except Exception as e:
            logger.error(f"Erro ao verificar entrega Full na página: {e}")
            return False

    def configure_magalu_filters(self, only_with_old_price=True, only_full_delivery=False):
        """Configura os filtros específicos para o Magalu"""
        self.only_with_old_price = only_with_old_price
        self.only_full_delivery = only_full_delivery
        logger.info(f"Filtros do Magalu configurados: apenas com preço antigo={only_with_old_price}, apenas com entrega Full={only_full_delivery}")

    def configure_amazon_filters(self, only_discount=True):
        """Configura os filtros específicos para a Amazon"""
        self.amazon_only_discount = only_discount
        logger.info(f"Filtros da Amazon configurados: apenas com desconto={only_discount}")


    def ensure_output_dir(self):
        """Garante que o diretório de saída existe"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

        # Criar diretório de debug
        debug_dir = os.path.join(self.output_dir, "debug")
        if not os.path.exists(debug_dir):
            os.makedirs(debug_dir)

    def _save_debug_html(self, html_content, url):
        """Salva o HTML da página para depuração"""
        try:
            debug_dir = os.path.join(self.output_dir, "debug")
            if not os.path.exists(debug_dir):
                os.makedirs(debug_dir)

            # Criar um nome de arquivo baseado na URL
            filename = re.sub(r'[^\w]', '_', url)
            filename = filename[:100]  # Limitar o tamanho do nome do arquivo

            # Adicionar timestamp para evitar sobrescrever arquivos
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filepath = os.path.join(debug_dir, f"{timestamp}_{filename}.html")

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)

        except Exception as e:
            logger.error(f"Erro ao salvar HTML para depuração: {e}")

    def load_categories(self):
        """Carrega as categorias do arquivo JSON para a loja atual"""
        try:
            current_store = self.store_manager.get_current_store()
            logger.info(f"Carregando categorias para a loja: {current_store}")

            with open(self.categories_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # Verificar se o formato é o novo (com lojas) ou o antigo
                if isinstance(data, dict):
                    # Novo formato com lojas
                    if current_store in data:
                        return data[current_store]
                    else:
                        logger.warning(f"Nenhuma categoria encontrada para a loja {current_store}")
                        return []
                else:
                    # Formato antigo (lista de categorias)
                    # Assumir que são do Mercado Livre
                    if current_store == "MercadoLivre":
                        return data
                    else:
                        logger.warning(f"Formato antigo de categorias, mas a loja atual é {current_store}")
                        return []
        except Exception as e:
            logger.error(f"Erro ao carregar categorias: {e}")
            return []

    def get_active_selectors(self, selector_type):
        """Retorna lista de seletores ativos para um determinado tipo"""
        selectors = self.selector_manager.get_selectors_by_type(selector_type)
        return [s[2] for s in selectors if s[4]]  # índice 2 é o seletor, 4 é active

    def extract_data(self, product_element, selector_type):
        """Extrai dados usando seletores gerenciados"""
        selectors = self.get_active_selectors(selector_type)

        # Para seletores de flash_deal e bestseller, verificamos a presença do texto
        if selector_type in ['flash_deal', 'bestseller', 'recommended']:
            for selector in selectors:
                try:
                    elements = product_element.select(selector)
                    if elements and len(elements) > 0:
                        return [elem.text.strip() for elem in elements if elem.text.strip()]
                except Exception as e:
                    logger.debug(f"Erro ao extrair {selector_type} com seletor {selector}: {str(e)}")
            return []

        # Para outros tipos de seletores
        for selector in selectors:
            try:
                element = product_element.select_one(selector)
                if element:
                    if selector_type == 'image':
                        # Tentar obter a URL da imagem de várias fontes possíveis
                        image_url = element.get('data-src', '') or element.get('src', '') or element.get('data-original', '')

                        # Verificar se a URL está completa
                        if image_url and not image_url.startswith(('http://', 'https://', '//')):
                            # Adicionar protocolo se começar com //
                            if image_url.startswith('//'):
                                image_url = 'https:' + image_url

                        logger.debug(f"URL de imagem extraída: {image_url[:50]}...")
                        return image_url
                    elif selector_type == 'link':
                        url = element.get('href', '')
                        return self.selector_manager.clean_product_url(url)
                    else:
                        return element.text.strip()
            except Exception as e:
                logger.debug(f"Erro ao extrair {selector_type} com seletor {selector}: {str(e)}")

        # Tratamento especial para o Magalu
        current_store = self.store_manager.get_current_store()
        if current_store == "Magalu":
            # Tratamento especial para links
            if selector_type == 'link':
                # Verificar se o próprio produto é um link (tag <a>)
                if product_element.name == 'a' and product_element.get('href'):
                    url = product_element.get('href')
                    return self.selector_manager.clean_product_url(url)

                # Tentar encontrar qualquer link no produto
                links = product_element.find_all('a')
                if links:
                    url = links[0].get('href', '')
                    return self.selector_manager.clean_product_url(url)

            # Tratamento especial para imagens
            elif selector_type == 'image':
                # Tentar encontrar a imagem usando data-testid específicos do Magalu
                img_elem = product_element.select_one('[data-testid="product-image"]')
                if img_elem:
                    image_url = img_elem.get('data-src', '') or img_elem.get('src', '') or img_elem.get('data-original', '')
                    logger.info(f"Imagem encontrada com data-testid=product-image: {image_url[:50]}...")

                    # Verificar se a URL está completa
                    if image_url and not image_url.startswith(('http://', 'https://', '//')):
                        # Adicionar protocolo se começar com //
                        if image_url.startswith('//'):
                            image_url = 'https:' + image_url

                    return image_url

                # Tentar encontrar qualquer imagem no produto
                images = product_element.find_all('img')
                if images:
                    for img in images:
                        image_url = img.get('data-src', '') or img.get('src', '') or img.get('data-original', '')
                        if image_url:
                            logger.info(f"Imagem encontrada em elemento genérico: {image_url[:50]}...")

                            # Verificar se a URL está completa
                            if not image_url.startswith(('http://', 'https://', '//')):
                                # Adicionar protocolo se começar com //
                                if image_url.startswith('//'):
                                    image_url = 'https:' + image_url

                            return image_url

                # Tentar encontrar imagens em tags picture
                picture_tags = product_element.find_all('picture')
                if picture_tags:
                    for picture in picture_tags:
                        img = picture.find('img')
                        if img:
                            image_url = img.get('data-src', '') or img.get('src', '') or img.get('data-original', '')
                            if image_url:
                                logger.info(f"Imagem encontrada em tag picture: {image_url[:50]}...")

                                # Verificar se a URL está completa
                                if not image_url.startswith(('http://', 'https://', '//')):
                                    # Adicionar protocolo se começar com //
                                    if image_url.startswith('//'):
                                        image_url = 'https:' + image_url

                                return image_url

            # Tratamento especial para títulos
            elif selector_type == 'title':
                # Tentar encontrar o título usando data-testid
                title_elem = product_element.select_one('[data-testid="title"]')
                if title_elem:
                    return title_elem.text.strip()

                # Tentar encontrar o título em qualquer elemento com texto
                for elem in product_element.find_all(['h1', 'h2', 'h3', 'h4', 'span', 'div']):
                    text = elem.get_text().strip()
                    if text and len(text) > 10 and len(text) < 200:
                        return text

            # Tratamento especial para cupons
            elif selector_type == 'coupon':
                # Tentar encontrar o cupom usando data-testid específico do Magalu
                coupon_elem = product_element.select_one('[data-testid="productCard-coupon"]')
                if coupon_elem:
                    coupon_text = coupon_elem.get_text().strip()
                    logger.info(f"Cupom encontrado com data-testid=productCard-coupon: {coupon_text}")
                    return coupon_text

                # Tentar encontrar cupom do Mercado Livre com seletor específico
                coupon_elem = product_element.select_one('div.poly-component__coupons span.poly-coupons__pill')
                if coupon_elem:
                    coupon_text = coupon_elem.get_text().strip()
                    logger.info(f"Cupom encontrado com seletor específico ML: {coupon_text}")
                    return coupon_text

                # Tentar encontrar qualquer elemento que contenha a palavra "Cupom" (apenas se não encontrou com seletores específicos)
                for elem in product_element.find_all(['p', 'span', 'div']):
                    text = elem.get_text().strip()
                    if "cupom" in text.lower() and len(text) < 50:  # Limitar tamanho para evitar textos longos
                        logger.info(f"Cupom encontrado em elemento genérico: {text}")
                        return text

            # Tratamento especial para preços
            elif selector_type == 'price':
                # Tentar encontrar o preço usando data-testid específicos do Magalu
                price_elem = product_element.select_one('[data-testid="price"]')
                if price_elem:
                    price_text = price_elem.get_text().strip()
                    logger.info(f"Preço encontrado com data-testid=price: {price_text}")
                    return price_text

                # Tentar encontrar o preço em elementos com classes específicas
                for price_class in ['price', 'product-price', 'current-price', 'final-price']:
                    price_elem = product_element.select_one(f'.{price_class}')
                    if price_elem:
                        price_text = price_elem.get_text().strip()
                        logger.info(f"Preço encontrado com classe {price_class}: {price_text}")
                        return price_text

                # Tentar encontrar qualquer elemento que contenha "R$"
                for elem in product_element.find_all(['p', 'span', 'div']):
                    text = elem.get_text().strip()
                    if "R$" in text:
                        logger.info(f"Preço encontrado em elemento genérico: {text}")
                        return text

            # Tratamento especial para preços antigos
            elif selector_type == 'old_price':
                # Tentar encontrar o preço antigo usando data-testid específicos do Magalu
                old_price_elem = product_element.select_one('[data-testid="old-price"]')
                if old_price_elem:
                    old_price_text = old_price_elem.get_text().strip()
                    logger.info(f"Preço antigo encontrado com data-testid=old-price: {old_price_text}")
                    return old_price_text

                # Tentar encontrar o preço antigo em elementos com classes específicas
                for old_price_class in ['old-price', 'original-price', 'list-price', 'regular-price']:
                    old_price_elem = product_element.select_one(f'.{old_price_class}')
                    if old_price_elem:
                        old_price_text = old_price_elem.get_text().strip()
                        logger.info(f"Preço antigo encontrado com classe {old_price_class}: {old_price_text}")
                        return old_price_text

                # Tentar encontrar tags del que geralmente indicam preço riscado
                del_elem = product_element.select_one('del')
                if del_elem:
                    old_price_text = del_elem.get_text().strip()
                    logger.info(f"Preço antigo encontrado em tag del: {old_price_text}")
                    return old_price_text

        return ""



    def clean_text(self, text):
        """Mantém o texto original com encoding adequado"""
        if not text:
            return ""

        try:
            # Substituir caracteres HTML comuns
            text = text.replace('&nbsp;', ' ').replace('\xa0', ' ')
            # Apenas remove quebras e espaços extras
            text = ' '.join(text.strip().split())
            # Remove aspas para evitar problemas com CSV
            text = text.replace('"', "'")
            return text
        except Exception as e:
            logger.error(f"Erro ao limpar texto: {e}")
            return ""

    def clean_price(self, price_text):
        """Limpa e converte um texto de preço para float"""
        if not price_text:
            return 0.0

        try:
            # Substituir caracteres HTML comuns
            price_text = price_text.replace('&nbsp;', ' ').replace('\xa0', ' ')

            # Remover todos os caracteres não numéricos exceto vírgula
            price_clean = re.sub(r'[^\d,]', '', price_text)

            # Substituir vírgula por ponto para conversão para float
            price_clean = price_clean.replace(',', '.')

            # Converter para float
            price_float = float(price_clean) if price_clean else 0.0

            logger.debug(f"Preço limpo: '{price_text}' -> '{price_clean}' -> {price_float}")
            return price_float
        except Exception as e:
            logger.warning(f"Erro ao converter preço '{price_text}': {str(e)}")
            return 0.0

    def has_old_price(self, product_element):
        """Verifica se um produto tem preço antigo (old_price)"""
        # Verificar se o produto tem preço antigo usando os seletores
        old_price = self.extract_data(product_element, 'old_price')

        # Se encontrou algum preço antigo, retorna True
        if old_price and old_price.strip():
            logger.info(f"Produto tem preço antigo: {old_price}")
            return True

        # Verificar especificamente para o Magalu
        current_store = self.store_manager.get_current_store()
        if current_store == "Magalu":
            # Verificar elementos com data-testid="price-original"
            old_price_elem = product_element.select_one('[data-testid="price-original"]')
            if old_price_elem:
                old_price_text = old_price_elem.get_text().strip()
                if old_price_text:
                    logger.info(f"Produto tem preço antigo (data-testid=price-original): {old_price_text}")
                    return True

            # Verificar tags del que geralmente indicam preço riscado
            del_elem = product_element.select_one('del')
            if del_elem:
                old_price_text = del_elem.get_text().strip()
                if old_price_text:
                    logger.info(f"Produto tem preço antigo (tag del): {old_price_text}")
                    return True

        logger.debug("Produto não tem preço antigo")
        return False

    def has_full_delivery(self, product_element):
        """Verifica se um produto tem entrega Full"""
        # Verificar se o produto tem a etiqueta "Full"
        current_store = self.store_manager.get_current_store()
        if current_store == "Magalu":
            try:
                # Definir seletores padrão para entrega Full
                full_delivery_selectors = [
                    '[data-testid="chip-label"] p',
                    'p.sc-dtInlm',
                    '.full-tag',
                    '[data-testid="productCard-shipping-tag"]',
                    'div[data-testid="productCard-shipping-tag"]',
                    'span.full-badge',
                    'span.full-tag',
                    'div.full-tag'
                ]

                # Verificar cada seletor
                for selector in full_delivery_selectors:
                    try:
                        elements = product_element.select(selector)
                        for elem in elements:
                            text = elem.get_text().strip()
                            if "Full" in text:
                                logger.info(f"Produto tem entrega Full (seletor: {selector})")
                                return True
                    except Exception as e:
                        logger.warning(f"Erro ao usar seletor '{selector}': {e}")
                        continue

                # Verificar elementos SVG com FastDeliveryIcon (caso especial)
                try:
                    svg_elements = product_element.select('svg use')
                    for svg_elem in svg_elements:
                        # Verificar o atributo diretamente
                        href_value = svg_elem.get('xlink:href')
                        if href_value and "#FastDeliveryIcon" in href_value:
                            logger.info("Produto tem entrega Full (FastDeliveryIcon)")
                            return True
                except Exception as e:
                    logger.warning(f"Erro ao verificar SVG FastDeliveryIcon: {e}")

                # Verificar diretamente no HTML se contém a palavra "Full"
                try:
                    shipping_elements = product_element.select('[data-testid="productCard-shipping-tag"]')
                    for elem in shipping_elements:
                        html_content = str(elem)
                        if "Full" in html_content:
                            logger.info("Produto tem entrega Full (encontrado no HTML)")
                            return True
                except Exception as e:
                    logger.warning(f"Erro ao verificar HTML para Full: {e}")

                # Verificar imagens que possam indicar entrega Full
                try:
                    img_elements = product_element.select('img')
                    for img in img_elements:
                        src = img.get('src', '')
                        alt = img.get('alt', '')
                        if 'full' in src.lower() or 'full' in alt.lower():
                            logger.info("Produto tem entrega Full (imagem)")
                            return True
                except Exception as e:
                    logger.warning(f"Erro ao verificar imagens para Full: {e}")

                # Verificar qualquer elemento de texto que contenha "Full"
                try:
                    # Buscar em todos os elementos de texto
                    text_elements = product_element.find_all(text=True)
                    for text in text_elements:
                        if "Full" in text:
                            logger.info(f"Produto tem entrega Full (texto encontrado: '{text.strip()}')")
                            return True

                    # Verificar no HTML completo do produto
                    html_content = str(product_element)
                    if "Full" in html_content and ("entrega" in html_content.lower() or "frete" in html_content.lower()):
                        logger.info("Produto tem entrega Full (encontrado no HTML completo)")
                        return True
                except Exception as e:
                    logger.warning(f"Erro ao verificar texto para Full: {e}")

            except Exception as e:
                logger.error(f"Erro ao verificar entrega Full: {e}")
                # Não falhar completamente se houver erro na verificação

        logger.debug("Produto não tem entrega Full")
        return False

    def extract_product_id(self, url):
        """Extrai o ID do produto da URL"""
        pattern = r"MLB-(\d+)"
        match = re.search(pattern, url)
        return match.group(1) if match else None


    async def process_product(self, product, title):
        """Processa um produto individual usando AI Manager"""
        try:
            url = self.extract_data(product, 'link')
            logger.debug(f"URL extraída: {url}")

            product_id = self.extract_product_id(url)
            logger.debug(f"ID do produto: {product_id}")

            if self.ai_manager.current_api == "OpenAI":
                # description = await self.ai_manager.generate_description_openAI(title)
                category, category_id = await self.ai_manager.get_category_openAI(title)
                subcategory, subcategory_id = await self.ai_manager.get_subcategory_openAI(title, category)
            elif self.ai_manager.current_api == "GoogleAI":
                # description = await self.ai_manager.generate_description_googleAI(title)
                category, category_id = await self.ai_manager.get_category_googleAI(title)
                subcategory, subcategory_id = await self.ai_manager.get_subcategory_googleAI(title, category)
            elif self.ai_manager.current_api == "OpenRouter":  # Mudado de else para elif específico
                # description = await self.ai_manager.generate_description_openRouter(title)
                category, category_id = await self.ai_manager.get_category_openRouter(title)
                subcategory, subcategory_id = await self.ai_manager.get_subcategory_openRouter(title, category)
            else:
                self.logger.error(f"API não reconhecida: {self.ai_manager.current_api}")
                return None

            # logger.debug(f"Descrição gerada: {description[:30]}...")
            logger.debug(f"Categoria determinada: {category}")
            logger.debug(f"Subcategoria determinada: {subcategory}")

            # Extrair dados básicos
            price = self.extract_data(product, 'price')
            logger.debug(f"Preço extraído: {price}")

            old_price = self.extract_data(product, 'old_price')
            logger.debug(f"Preço antigo extraído: {old_price}")

            image_url = self.extract_data(product, 'image')
            logger.debug(f"URL da imagem extraída: {image_url[:30]}...")

            # installments = self.extract_data(product, 'installments')
            # logger.debug(f"Informações de parcelas extraídas: {installments}")

            coupon_info = self.extract_data(product, 'coupon')
            logger.debug(f"Informações de cupom extraídas: {coupon_info}")

            # Processar tipo de oferta
            flash_deal_tags = self.extract_data(product, 'flash_deal')
            bestseller_tags = self.extract_data(product, 'bestseller')
            recommended_tags = self.extract_data(product, 'recommended')

            flash_deal = bool(flash_deal_tags) if isinstance(flash_deal_tags, list) else False
            bestseller = bool(bestseller_tags) if isinstance(bestseller_tags, list) else False
            recommended = bool(recommended_tags) if isinstance(recommended_tags, list) else False
            # Processar tipo de oferta
            offer_type = self.selector_manager.process_offer_type(flash_deal, bestseller, recommended)

            logger.info(f"Extração completa para o produto: {title}")
            # Conversão para float
            # Converter preços usando o método especializado
            price_float = self.clean_price(price)
            logger.info(f"Preço convertido para float: {price_float}")

            old_price_float = self.clean_price(old_price)
            logger.info(f"Preço antigo convertido para float: {old_price_float}")

            # Limpa a URL do produto para remover parâmetros de rastreamento
            clean_url = self.selector_manager.clean_product_url(url)

            # Obter informações da loja atual
            current_store = self.store_manager.get_current_store()
            store_info = self.store_manager.get_store_info()

            # Ajustar valores específicos para cada loja
            platform_name = store_info["name"]

            # Verificar se a URL está completa
            if current_store == "Magalu" and not clean_url.startswith("http"):
                # Adicionar domínio base se necessário
                if clean_url.startswith("/"):
                    clean_url = f"https://www.magazinevoce.com.br{clean_url}"
                else:
                    clean_url = f"https://www.magazinevoce.com.br/{clean_url}"

            # Verificar se a URL da imagem está completa
            if current_store == "Magalu" and image_url and not image_url.startswith("http"):
                # Adicionar domínio base se necessário
                if image_url.startswith("//"):
                    image_url = f"https:{image_url}"
                elif image_url.startswith("/"):
                    image_url = f"https://www.magazinevoce.com.br{image_url}"
                else:
                    image_url = f"https://www.magazinevoce.com.br/{image_url}"

            # Para o Magalu, a URL afiliada é a mesma que a URL do produto
            url_afiliado = ""
            if current_store == "Magalu":
                url_afiliado = clean_url
                logger.info(f"URL afiliada do Magalu: {url_afiliado}")

            return {
                "plataforma": platform_name,
                "url_produto": clean_url,
                "url_afiliado": url_afiliado,
                "url_imagem": image_url,
                "titulo": title,
                "categoria": category,
                "categoria_id": category_id,
                "subcategoria": subcategory,
                "subcategoria_id": subcategory_id,
                "descricao": "",
                "preco_atual": price_float,
                "preco_antigo": old_price_float,
                "preco_alternativo": 0.0,
                "ativo": True,
                "cupom": coupon_info,
                "menor_preco": False,
                "indicamos": offer_type in ["Recomendado", "Oferta Relâmpago", "Mais Vendido"],
                "disparar_whatsapp": False,
                "grupo_whatsapp": "",
                "frete": False,
                "invalidProduct": False,
                "isStory": False,
                "image_id": ""
            }

        except Exception as e:
            logger.error(f"Erro ao processar produto: {e}")
            return None
    async def process_category(self, category):
        """Processa uma categoria de forma assíncrona"""
        try:
            self.stats = {
                "produtos_encontrados": 0,
                "produtos_processados": 0,
                "produtos_com_erro": 0,
                "produtos_salvos": 0
            }
            self.set_status(f"Processando categoria: {category['name']}")
            logger.info(f"Processando categoria: {category['name']}")
            name = category['name']
            url_template = category['url_template']
            max_page = category.get('max_page', 1)

            # Garantir que o diretório de saída existe
            if not os.path.exists(self.output_dir):
                os.makedirs(self.output_dir)
                logger.info(f"Diretório de saída criado: {self.output_dir}")

            output_file = os.path.join(self.output_dir, f"{name.replace(' ', '_')}.csv")
            logger.info(f"Arquivo de saída: {output_file}")

            # Cabeçalho do CSV baseado no modelo Product
            headers = [
                "plataforma", "url_produto", "url_afiliado", "url_imagem", "titulo",
                "categoria", "categoria_id", "subcategoria", "subcategoria_id", "descricao", "preco_atual",
                "preco_antigo", "preco_alternativo", "ativo", "cupom",
                "menor_preco", "indicamos", "disparar_whatsapp",
                "grupo_whatsapp", "frete", "invalidProduct", "isStory", "image_id"
            ]

            # Criar ou sobrescrever arquivo CSV com cabeçalho usando UTF-8 com BOM
            with open(output_file, 'w', encoding='utf-8-sig', newline='') as f:
                f.write(",".join(headers) + "\n")
                logger.debug(f"Arquivo CSV criado: {output_file}")

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            for page in range(1, max_page + 1):
                if not self._is_running:
                    logger.info("Interrompendo processamento da categoria")
                    return

                # Formatar URL de acordo com a loja atual
                current_store = self.store_manager.get_current_store()
                store_info = self.store_manager.get_store_info()

                if current_store == "MercadoLivre":
                    # Formato do Mercado Livre
                    url = url_template.format(i=page)
                elif current_store == "Magalu":
                    # Formato da Magalu
                    if "?" in url_template:
                        # Se já tem parâmetros, adiciona a página
                        url = f"{url_template}&page={page}"
                    else:
                        # Se não tem parâmetros, adiciona a página
                        url = f"{url_template}?page={page}"
                elif current_store == "Amazon":
                    # Formato da Amazon (usa índice de início baseado na página)
                    start_index = (page - 1) * 60  # Amazon usa 60 produtos por página
                    url = url_template.format(i=start_index)
                else:
                    # Formato genérico
                    url = url_template.format(i=page)

                logger.info(f"Processando página {page} de {max_page} - URL: {url}")
                headers = self.header_manager.get_random_header()
                try:
                    response = self.session.get(url, headers=headers, timeout=10)
                    response.raise_for_status()

                    if response.status_code == 200:
                        soup = BeautifulSoup(response.text, 'html.parser')

                        # Verificar se a página contém produtos com entrega Full quando o filtro estiver ativado
                        current_store = self.store_manager.get_current_store()
                        if current_store == "Magalu" and self.only_full_delivery:
                            has_full_delivery = self.check_full_delivery_in_page(soup, url)
                            if not has_full_delivery:
                                logger.warning(f"Página {page} não contém produtos com entrega Full, pulando...")
                                continue

                        # Obtém seletores ativos para produtos
                        product_selectors = self.get_active_selectors('product')

                        # Tenta cada seletor até encontrar produtos
                        products = []
                        for selector in product_selectors:
                            products = soup.select(selector)
                            if products:
                                logger.info(f"Produtos encontrados usando seletor: {selector}")

                                # Depurar o primeiro produto encontrado
                                if page == 1 and current_store == "Magalu" and not hasattr(self, '_debug_done'):
                                    logger.info("=== DEPURANDO PRIMEIRO PRODUTO ===")
                                    first_product = products[0]

                                    # Verificar se é um link
                                    logger.info(f"Tipo do elemento: {first_product.name}")
                                    if first_product.name == 'a':
                                        logger.info(f"Href: {first_product.get('href')}")

                                    # Verificar atributos data-testid
                                    testid_elements = first_product.select('[data-testid]')
                                    for elem in testid_elements:
                                        logger.info(f"Elemento com data-testid: {elem.name} - {elem.get('data-testid')} - {elem.get_text().strip()[:50]}")

                                    # Verificar elementos de texto
                                    text_elements = first_product.find_all(['h1', 'h2', 'h3', 'h4', 'span', 'div'])
                                    for i, elem in enumerate(text_elements[:10]):
                                        text = elem.get_text().strip()
                                        if text:
                                            logger.info(f"Elemento de texto {i}: {elem.name} - {text[:50]}")

                                    self._debug_done = True
                                    logger.info("=== FIM DA DEPURAÇÃO ===")

                                break

                        if not products:
                            logger.warning(f"Nenhum produto encontrado na página {page}")
                            continue

                        for product in products:
                            if not self._is_running:
                                logger.info("Interrompendo processamento dos produtos")
                                return

                            # Verificar filtros para o Magalu e Amazon
                            current_store = self.store_manager.get_current_store()
                            if current_store == "Magalu":
                                # Verificar se deve filtrar por preço antigo
                                if self.only_with_old_price and not self.has_old_price(product):
                                    logger.info("Produto sem preço antigo, pulando...")
                                    continue

                                # Verificar se deve filtrar por entrega Full
                                if self.only_full_delivery and not self.has_full_delivery(product):
                                    logger.info("Produto sem entrega Full, pulando...")
                                    continue
                            elif current_store == "Amazon":
                                # Verificar se deve filtrar por desconto
                                if self.amazon_only_discount and not self.has_old_price(product):
                                    logger.info("Produto Amazon sem desconto, pulando...")
                                    continue

                            self.stats["produtos_encontrados"] += 1

                            try:
                                # Extrair título primeiro
                                current_title = self.extract_data(product, 'title')

                                # Para o Magalu, tentar extrair o título de forma mais agressiva
                                if not current_title and self.store_manager.get_current_store() == "Magalu":
                                    # Tentar encontrar o título usando data-testid específico do Magalu
                                    title_elem = product.select_one('[data-testid="product-title"]')
                                    if title_elem:
                                        current_title = title_elem.text.strip()
                                        logger.info(f"Título encontrado com data-testid=product-title: {current_title[:30]}...")
                                    else:
                                        # Tentar outros data-testid
                                        title_elem = product.select_one('[data-testid="title"]')
                                        if title_elem:
                                            current_title = title_elem.text.strip()
                                            logger.info(f"Título encontrado com data-testid=title: {current_title[:30]}...")
                                        else:
                                            # Tentar encontrar o título em qualquer elemento com texto
                                            for elem in product.find_all(['h1', 'h2', 'h3', 'h4', 'span', 'div']):
                                                text = elem.get_text().strip()
                                                if text and len(text) > 10 and len(text) < 200:
                                                    current_title = text
                                                    logger.info(f"Título encontrado em elemento genérico: {text[:30]}...")
                                                    break

                                # Para o Mercado Livre, tentar extrair o título de forma específica
                                elif not current_title and self.store_manager.get_current_store() == "MercadoLivre":
                                    # Seletores específicos do Mercado Livre
                                    ml_title_selectors = [
                                        'h2.ui-search-item__title',
                                        '.ui-search-item__title',
                                        'h2.poly-box',
                                        '.poly-component__title',
                                        'a.ui-search-item__group__element',
                                        'h2'
                                    ]

                                    for selector in ml_title_selectors:
                                        title_elem = product.select_one(selector)
                                        if title_elem:
                                            current_title = title_elem.text.strip()
                                            logger.info(f"Título ML encontrado com seletor {selector}: {current_title[:30]}...")
                                            break

                                    # Se ainda não encontrou, tentar busca genérica
                                    if not current_title:
                                        for elem in product.find_all(['h1', 'h2', 'h3', 'h4', 'span', 'a']):
                                            text = elem.get_text().strip()
                                            if text and len(text) > 10 and len(text) < 200:
                                                current_title = text
                                                logger.info(f"Título ML encontrado em elemento genérico: {text[:30]}...")
                                                break

                                if not current_title:
                                    logger.warning("Produto sem título encontrado, pulando...")
                                    continue

                                # Atualizar status com o título do produto atual e modelo (se OpenRouter)
                                status_message = f"Processando produto: {current_title}"
                                if self.ai_manager.current_api == "OpenRouter" and hasattr(self.ai_manager, 'current_model'):
                                    status_message += f" | Modelo: {self.ai_manager.current_model}"
                                self.set_status(status_message)

                                # Processar o produto
                                try:
                                    # Definir um timeout para o processamento do produto
                                    product_data = await asyncio.wait_for(
                                        self.process_product(product, current_title),
                                        timeout=10  # 10 segundos de timeout
                                    )

                                    if not product_data:
                                        logger.warning(f"Falha ao processar produto: {current_title}")
                                        self.stats["produtos_com_erro"] += 1
                                        continue
                                except asyncio.TimeoutError:
                                    logger.warning(f"Timeout ao processar produto: {current_title}")
                                    self.stats["produtos_com_erro"] += 1
                                    continue
                                except Exception as e:
                                    logger.error(f"Erro inesperado ao processar produto: {current_title} - {str(e)}")
                                    self.stats["produtos_com_erro"] += 1
                                    continue

                                # Limpar e validar dados
                                product_data = {k: self.clean_text(str(v)) for k, v in product_data.items()}
                                self.stats["produtos_processados"] += 1

                                # Salvar no CSV
                                with open(output_file, 'a', encoding='utf-8') as f:
                                    values = [f'"{v}"' for v in product_data.values()]
                                    f.write(",".join(values) + "\n")
                                    self.stats["produtos_salvos"] += 1

                                logger.info(f"✅ Produto processado e salvo: {current_title}")

                            except Exception as e:
                                logger.error(f"Erro ao processar produto individual: {e}")
                                self.stats["produtos_com_erro"] += 1
                                continue

                    await asyncio.sleep(3)

                except Exception as e:
                    logger.error(f"Erro ao processar página {page}: {e}")
                    continue

            self.log_resumo_categoria()

        except Exception as e:
            error_msg = f"Erro ao processar categoria {category['name']}: {str(e)}"
            self.set_status(error_msg)
            logger.error(error_msg)
    def log_resumo_categoria(self):
        """Loga o resumo da categoria processada"""
        logger.info("=== RESUMO DA CATEGORIA ===")
        logger.info(f"Produtos encontrados: {self.stats['produtos_encontrados']}")
        logger.info(f"Produtos processados com sucesso: {self.stats['produtos_processados']}")
        logger.info(f"Produtos com erro: {self.stats['produtos_com_erro']}")
        logger.info(f"Produtos salvos no CSV: {self.stats['produtos_salvos']}")

        if self.stats['produtos_encontrados'] > 0:
            taxa = (self.stats['produtos_salvos'] / self.stats['produtos_encontrados']) * 100
            logger.info(f"Taxa de sucesso: {taxa:.2f}%")
        else:
            logger.info("Taxa de sucesso: N/A (nenhum produto encontrado)")
if __name__ == "__main__":
    # Configurar logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.FileHandler('logs/scraper.log', encoding='utf-8', errors='ignore'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    # Forçar encoding UTF-8 para stdout
    if sys.stdout.encoding != 'utf-8':
        sys.stdout.reconfigure(encoding='utf-8')

    logger.info("Iniciando script do scraper")

    try:
        scraper = Scraper()
        scraper.start()  # Agora usando o novo método start()

        # Aguarda o término do scraper
        while scraper.is_running():
            time.sleep(1)

        logger.info("Processamento concluído com sucesso")
        sys.exit(0)

    except KeyboardInterrupt:
        if scraper:
            scraper.stop()
        logger.info("Scraper interrompido pelo usuário")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Erro fatal no scraper: {e}")
        sys.exit(1)












