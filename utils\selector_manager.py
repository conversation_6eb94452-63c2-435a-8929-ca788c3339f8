import logging
import os
import sys
import threading
import re
import json
from venv import logger
from utils.store_manager import StoreManager


class SelectorManager:
    def __init__(self, store_manager=None):
        self.setup_logging()
        self.store_manager = store_manager if store_manager else StoreManager()
        self.selector_map = {}
        self.selector_cache = {}
        self.current_status = "Inicializado"
        self.lock = threading.Lock()
        self.selectors_file = "selectors.json"

        # Definir seletores padrão para Mercado Livre
        self.product_selectors = [
            'li.ui-search-layout__item',
            'div.ui-search-result',
            'div.promotion-item',
            'div.andes-card.poly-card--grid-card'
        ]

        self.title_selectors = [
            'h2.ui-search-item__title',
            'h2.promotion-item__title',
            'span.ui-search-item__title',
            'a.poly-component__title'
        ]

        self.price_selectors = [
            'span.price-tag-amount',
            'span.ui-search-price__part',
            'span.promotion-item__price',
            'div.poly-price__current .andes-money-amount__fraction',
            'div.poly-price__current .andes-money-amount__cents'
        ]

        self.old_price_selectors = [
            's.andes-money-amount--previous .andes-money-amount__fraction',
            'span.andes-money-amount--previous .andes-money-amount__fraction',
            'span.price-tag-amount-previous'
        ]

        self.link_selectors = [
            'a.ui-search-link',
            'a.promotion-item__link',
            'a.poly-component__title'
        ]

        self.image_selectors = [
            'img.ui-search-result-image__element',
            'img.promotion-item__img',
            'img[data-src]',
            'img.poly-component__picture'
        ]

        self.installment_selectors = [
            'span.poly-price__installments',
            'span.ui-search-installments',
            'span.promotion-item__installments'
        ]

        self.coupon_selectors = [
            'span.andes-money-amount__discount',
            'span.ui-search-price__discount',
            'span.promotion-item__discount'
        ]

        self.flash_deal_selectors = [
            "span.poly-component__highlight:contains('OFERTA RELÂMPAGO')",
            "span.poly-component__highlight:contains('Oferta relâmpago')",
            "div.poly-component__tags span.poly-tags__pill:contains('Oferta relâmpago')"
        ]

        self.bestseller_selectors = [
            "span.poly-component__highlight:contains('MAIS VENDIDO')",
            "span.poly-component__highlight:contains('Mais vendido')",
            "div.poly-component__tags span.poly-tags__pill:contains('Mais vendido')"
        ]

        self.recommended_selectors = [
            "span.poly-component__highlight:contains('RECOMENDADO')",
            "span.poly-component__highlight svg[href='#poly_meli'] + :contains('RECOMENDADO')"
        ]

        # Adicionar seletores para Magalu
        self.magalu_product_selectors = [
            'a[data-testid="product-card-container"]',
            'div.product-card',
            'div.product-item',
            'li.product-list-item',
            'div.productCard',
            'div.sc-dPiLbb',
            'div.sc-hKwDye',
            'div.sc-jrQzAO',
            'div.sc-kDTinF',
            'div.sc-iqseJM'
        ]

        self.magalu_title_selectors = [
            'h2[data-testid="product-title"]',
            'h2[data-testid="title"]',
            'span[data-testid="title"]',
            'div[data-testid="title"]',
            'h3.product-title',
            'h2.product-name',
            'div.product-card__title',
            'h2.sc-eCImPb',
            'h3.sc-gsDKAQ',
            'span.sc-hKwDye',
            'div.sc-jrQzAO',
            'span.sc-dtInlm'
        ]

        self.magalu_price_selectors = [
            'p[data-testid="price-value"]',
            'p[data-testid="installment"]',
            'span.price-current',
            'div.product-price__current',
            'span.product-card__price',
            'p.sc-gsDKAQ',
            'span.sc-hKwDye',
            'div.sc-jrQzAO',
            'p.sc-eCImPb',
            'span[data-testid="price"]',
            'p[data-testid="product-price"]',
            'span.price',
            'div.price',
            'span.product-price',
            'div.product-price',
            'span.current-price',
            'div.current-price',
            'span.final-price',
            'div.final-price'
        ]

        self.magalu_old_price_selectors = [
            'p[data-testid="price-original"]',
            'span.price-old',
            'div.product-price__old',
            'span.product-card__old-price',
            's.sc-gsDKAQ',
            's.sc-hKwDye',
            'div.sc-jrQzAO s',
            'span[data-testid="old-price"]',
            'p[data-testid="product-old-price"]',
            'span.old-price',
            'div.old-price',
            'span.original-price',
            'div.original-price',
            'span.list-price',
            'div.list-price',
            'span.regular-price',
            'div.regular-price',
            'del'  # Tag del geralmente indica preço riscado
        ]

        self.magalu_link_selectors = [
            'a.product-card__link',
            'a.product-item__link',
            'a.product-list-item__link',
            'a.sc-gsDKAQ',
            'a.sc-hKwDye',
            'a.sc-jrQzAO',
            'a[data-testid="product-card-container"]'
        ]

        self.magalu_image_selectors = [
            'img.product-card__image',
            'img.product-item__image',
            'img.product-list-item__image',
            'img.sc-gsDKAQ',
            'img.sc-hKwDye',
            'img.sc-jrQzAO',
            'img[data-testid="image"]',
            'img[data-testid="product-image"]',
            'img[data-testid="product-card-image"]',
            'img',  # Seletor genérico como último recurso
            'picture img'  # Imagens dentro de tags picture
        ]

        # Seletores para cupons do Magalu
        self.magalu_coupon_selectors = [
            'div[data-testid="productCard-coupon"]',
            'p:contains("Cupom")',
            'div.sc-hqpNSm',
            'div.bxDFiO'
        ]

        # Inicializar seletores
        self.load_from_json()

        # Definir tipos de seletores
        self.selector_types = {
            'product': 'Seletores de produtos',
            'title': 'Seletores de título',
            'price': 'Seletores de preço atual',
            'old_price': 'Seletores de preço antigo',
            'link': 'Seletores de link',
            'image': 'Seletores de imagem',
            'installments': 'Seletores de parcelas',
            'coupon': 'Seletores de cupom',
            'flash_deal': 'Seletores de oferta relâmpago',
            'bestseller': 'Seletores de mais vendidos',
            'recommended': 'Seletores de recomendados',
            'offer_type': 'Seletores de tipo de oferta'
        }

        # Adicionar seletores para offer_type
        self.offer_type_selectors = [
            'span.promotion-type',
            'span.offer-badge',
            'span.discount-tag'
        ]

        # Seletores para tipos de oferta
        self.flash_deal_selectors = [
            "span.poly-component__highlight:contains('OFERTA RELÂMPAGO')",
            "span.poly-component__highlight:contains('Oferta relâmpago')",
            "div.poly-component__tags span.poly-tags__pill:contains('Oferta relâmpago')"
        ]

        self.bestseller_selectors = [
            "span.poly-component__highlight:contains('MAIS VENDIDO')",
            "span.poly-component__highlight:contains('Mais vendido')",
            "div.poly-component__tags span.poly-tags__pill:contains('Mais vendido')"
        ]
        self.recommended_selectors = [
            "span.poly-component__highlight:contains('RECOMENDADO')",
            "span.poly-component__highlight svg[href='#poly_meli'] + :contains('RECOMENDADO')"
        ]

    def load_from_json(self):
        """Carrega seletores do arquivo JSON"""
        try:
            if os.path.exists(self.selectors_file):
                with open(self.selectors_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # Verificar se o formato é o novo (com lojas) ou o antigo
                    if isinstance(data, dict) and all(store in data for store in self.store_manager.get_store_names()):
                        # Novo formato com lojas
                        self.selector_map = data
                        self.logger.info("Seletores carregados do arquivo JSON (formato com lojas)")
                        return True

            # Se chegou aqui, precisa inicializar os seletores
            self._initialize_selector_map()
            self.save_to_json()
            return True
        except Exception as e:
            self.logger.error(f"Erro ao carregar seletores do JSON: {e}")
            self._initialize_selector_map()
            return False

    def save_to_json(self):
        """Salva os seletores no arquivo JSON"""
        try:
            with open(self.selectors_file, 'w', encoding='utf-8') as f:
                json.dump(self.selector_map, f, indent=4, ensure_ascii=False)
            self.logger.info("Seletores salvos no arquivo JSON")
            return True
        except Exception as e:
            self.logger.error(f"Erro ao salvar seletores no JSON: {e}")
            return False

    def _initialize_selector_map(self):
        """Inicializa o mapa de seletores com IDs únicos"""
        self.logger.info("Inicializando mapa de seletores")
        current_id = 0
        self.selector_map = {}

        # Função auxiliar para criar seletores com IDs incrementais
        def create_selectors_with_ids(selector_type, selectors):
            nonlocal current_id
            result = [
                {
                    'id': current_id + i,
                    'type': selector_type,
                    'selector': selector,
                    'description': f'Seletor para {selector_type}',
                    'active': True
                }
                for i, selector in enumerate(selectors)
            ]
            current_id += len(selectors)
            return result

        # Inicializar para cada loja
        for store in self.store_manager.get_store_names():
            self.selector_map[store] = {}

            if store == "MercadoLivre":
                # Seletores para Mercado Livre
                self.selector_map[store]['product'] = create_selectors_with_ids('product', self.product_selectors)
                self.selector_map[store]['title'] = create_selectors_with_ids('title', self.title_selectors)
                self.selector_map[store]['price'] = create_selectors_with_ids('price', self.price_selectors)
                self.selector_map[store]['old_price'] = create_selectors_with_ids('old_price', self.old_price_selectors)
                self.selector_map[store]['link'] = create_selectors_with_ids('link', self.link_selectors)
                self.selector_map[store]['image'] = create_selectors_with_ids('image', self.image_selectors)
                self.selector_map[store]['installments'] = create_selectors_with_ids('installments', self.installment_selectors)
                self.selector_map[store]['coupon'] = create_selectors_with_ids('coupon', self.coupon_selectors)
                self.selector_map[store]['flash_deal'] = create_selectors_with_ids('flash_deal', self.flash_deal_selectors)
                self.selector_map[store]['bestseller'] = create_selectors_with_ids('bestseller', self.bestseller_selectors)
                self.selector_map[store]['recommended'] = create_selectors_with_ids('recommended', self.recommended_selectors)

            elif store == "Magalu":
                # Seletores para Magalu
                self.selector_map[store]['product'] = create_selectors_with_ids('product', self.magalu_product_selectors)
                self.selector_map[store]['title'] = create_selectors_with_ids('title', self.magalu_title_selectors)
                self.selector_map[store]['price'] = create_selectors_with_ids('price', self.magalu_price_selectors)
                self.selector_map[store]['old_price'] = create_selectors_with_ids('old_price', self.magalu_old_price_selectors)
                self.selector_map[store]['link'] = create_selectors_with_ids('link', self.magalu_link_selectors)
                self.selector_map[store]['image'] = create_selectors_with_ids('image', self.magalu_image_selectors)

                # Usar os seletores específicos do Magalu para cupom
                self.selector_map[store]['coupon'] = create_selectors_with_ids('coupon', self.magalu_coupon_selectors)

                # Usar os mesmos seletores do Mercado Livre para os tipos que não têm seletores específicos para Magalu
                self.selector_map[store]['installments'] = create_selectors_with_ids('installments', self.installment_selectors)
                self.selector_map[store]['flash_deal'] = create_selectors_with_ids('flash_deal', self.flash_deal_selectors)
                self.selector_map[store]['bestseller'] = create_selectors_with_ids('bestseller', self.bestseller_selectors)
                self.selector_map[store]['recommended'] = create_selectors_with_ids('recommended', self.recommended_selectors)

            else:
                # Para outras lojas, usar os seletores padrão do Mercado Livre
                self.selector_map[store]['product'] = create_selectors_with_ids('product', self.product_selectors)
                self.selector_map[store]['title'] = create_selectors_with_ids('title', self.title_selectors)
                self.selector_map[store]['price'] = create_selectors_with_ids('price', self.price_selectors)
                self.selector_map[store]['old_price'] = create_selectors_with_ids('old_price', self.old_price_selectors)
                self.selector_map[store]['link'] = create_selectors_with_ids('link', self.link_selectors)
                self.selector_map[store]['image'] = create_selectors_with_ids('image', self.image_selectors)
                self.selector_map[store]['installments'] = create_selectors_with_ids('installments', self.installment_selectors)
                self.selector_map[store]['coupon'] = create_selectors_with_ids('coupon', self.coupon_selectors)
                self.selector_map[store]['flash_deal'] = create_selectors_with_ids('flash_deal', self.flash_deal_selectors)
                self.selector_map[store]['bestseller'] = create_selectors_with_ids('bestseller', self.bestseller_selectors)
                self.selector_map[store]['recommended'] = create_selectors_with_ids('recommended', self.recommended_selectors)

    def validate_selector_dict(self, selector):
        """Valida se um dicionário de seletor está completo e correto"""
        required_fields = ['id', 'type', 'selector', 'description', 'active']

        # Verifica se todos os campos requeridos existem
        if not all(field in selector for field in required_fields):
            self.logger.warning(f"Campos obrigatórios faltando no seletor: {selector}")
            return False

        # Verifica se os tipos dos campos estão corretos
        if not isinstance(selector['id'], int):
            self.logger.warning(f"ID inválido no seletor: {selector}")
            return False
        if not isinstance(selector['type'], str):
            self.logger.warning(f"Tipo inválido no seletor: {selector}")
            return False
        if not isinstance(selector['selector'], str):
            self.logger.warning(f"String do seletor inválida: {selector}")
            return False
        if not isinstance(selector['description'], str):
            self.logger.warning(f"Descrição inválida no seletor: {selector}")
            return False
        if not isinstance(selector['active'], bool):
            self.logger.warning(f"Campo 'active' inválido no seletor: {selector}")
            return False

        return True

    def validate_selectors(self):
        """Validates all selectors in the selector map"""
        self.logger.info("Iniciando validação de todos os seletores...")
        valid = True

        # Carregar seletores do arquivo JSON se existir
        self.load_from_json()

        for store, store_selectors in self.selector_map.items():
            self.logger.debug(f"Validando seletores da loja: {store}")
            for selector_type, selectors in store_selectors.items():
                self.logger.debug(f"Validando seletores do tipo: {selector_type}")
                for selector in selectors:
                    if not self.validate_selector_dict(selector):
                        self.logger.error(f"Seletor inválido encontrado: {selector}")
                        valid = False

        if valid:
            self.logger.info("Todos os seletores são válidos")
        else:
            self.logger.warning("Foram encontrados seletores inválidos")
        return valid

    def get_all_selectors(self):
        """Returns all selectors of the current store as a list of tuples for UI compatibility"""
        current_store = self.store_manager.get_current_store()
        self.logger.debug(f"Obtendo lista de todos os seletores da loja: {current_store}")

        if current_store not in self.selector_map:
            self.logger.warning(f"Loja {current_store} não encontrada no mapa de seletores")
            return []

        all_selectors = []
        for selector_type, selectors in self.selector_map[current_store].items():
            for selector in selectors:
                selector_tuple = (
                    selector['id'],
                    selector['type'],
                    selector['selector'],
                    selector['description'],
                    1 if selector['active'] else 0,
                    None
                )
                all_selectors.append(selector_tuple)

        self.logger.info(f"Total de seletores encontrados: {len(all_selectors)}")
        return all_selectors

    def get_selectors_by_type(self, selector_type):
        """Returns selectors of a specific type from the current store as tuples for UI compatibility"""
        current_store = self.store_manager.get_current_store()
        cache_key = f"{current_store}_{selector_type}"

        # Verifica se os seletores já estão em cache
        if cache_key in self.selector_cache:
            return self.selector_cache[cache_key]

        # Se não estiver em cache, busca e armazena
        if current_store not in self.selector_map:
            self.logger.warning(f"Loja {current_store} não encontrada no mapa de seletores")
            return []

        selectors = self.selector_map[current_store].get(selector_type, [])
        result = [
            (
                selector['id'],
                selector['type'],
                selector['selector'],
                selector['description'],
                1 if selector['active'] else 0,
                None
            )
            for selector in selectors
        ]

        # Registra apenas na primeira vez que busca cada tipo
        if not self.selector_cache:  # Se é a primeira busca de qualquer tipo
            self.logger.info("Iniciando carregamento de seletores...")
        if cache_key not in self.selector_cache:  # Se é a primeira busca deste tipo
            self.logger.info(f"Carregados {len(result)} seletores do tipo {selector_type} para a loja {current_store}")

        # Armazena no cache
        self.selector_cache[cache_key] = result
        return result

    def clear_cache(self):
        """Limpa o cache de seletores"""
        self.selector_cache.clear()
        self.logger.debug("Cache de seletores limpo")

    def add_selector(self, selector_type, selector_text, description=None):
        """Adiciona um novo seletor"""
        current_store = self.store_manager.get_current_store()
        self.clear_cache()  # Limpa o cache quando adiciona novo seletor
        self.logger.info(f"Adicionando novo seletor do tipo: {selector_type} para a loja: {current_store}")

        if current_store not in self.selector_map:
            self.logger.debug(f"Criando mapa para a loja: {current_store}")
            self.selector_map[current_store] = {}

        if selector_type not in self.selector_map[current_store]:
            self.logger.debug(f"Criando nova lista para o tipo: {selector_type}")
            self.selector_map[current_store][selector_type] = []

        description = description or f'Seletor para {selector_type}'
        new_selector = {
            'id': max([s[0] for s in self.get_all_selectors()], default=-1) + 1,
            'type': selector_type,
            'selector': selector_text,
            'description': description,
            'active': True
        }

        self.selector_map[current_store][selector_type].append(new_selector)
        self.save_to_json()  # Salvar alterações no arquivo JSON
        self.logger.info(f"Seletor adicionado com sucesso. ID: {new_selector['id']}")
        return new_selector

    def update_selector(self, selector_id, selector_type, selector_text, description=None, active=True):
        """Atualiza um seletor existente"""
        current_store = self.store_manager.get_current_store()
        self.clear_cache()  # Limpa o cache quando atualiza seletor
        self.logger.info(f"Atualizando seletor ID: {selector_id} para a loja: {current_store}")

        if current_store not in self.selector_map:
            self.logger.warning(f"Loja {current_store} não encontrada no mapa de seletores")
            return False

        for type_name, selectors in self.selector_map[current_store].items():
            for i, selector in enumerate(selectors):
                if selector['id'] == selector_id:
                    description = description or f'Seletor para {selector_type}'
                    updated_selector = {
                        'id': selector_id,
                        'type': selector_type,
                        'selector': selector_text,
                        'description': description,
                        'active': active
                    }

                    # Se o tipo mudou, mover para a nova lista
                    if type_name != selector_type:
                        # Remover do tipo atual
                        self.selector_map[current_store][type_name].pop(i)

                        # Adicionar ao novo tipo
                        if selector_type not in self.selector_map[current_store]:
                            self.selector_map[current_store][selector_type] = []
                        self.selector_map[current_store][selector_type].append(updated_selector)
                    else:
                        # Atualizar no mesmo tipo
                        self.selector_map[current_store][type_name][i] = updated_selector

                    self.save_to_json()  # Salvar alterações no arquivo JSON
                    self.logger.info("Seletor atualizado com sucesso")
                    return True

        self.logger.warning(f"Seletor ID {selector_id} não encontrado")
        return False

    def delete_selector(self, selector_id):
        """Remove um seletor"""
        current_store = self.store_manager.get_current_store()
        self.clear_cache()  # Limpa o cache quando remove seletor
        self.logger.info(f"Removendo seletor ID: {selector_id} da loja: {current_store}")

        if current_store not in self.selector_map:
            self.logger.warning(f"Loja {current_store} não encontrada no mapa de seletores")
            return False

        for selector_type, selectors in self.selector_map[current_store].items():
            original_length = len(selectors)
            self.selector_map[current_store][selector_type] = [s for s in selectors if s['id'] != selector_id]
            if len(self.selector_map[current_store][selector_type]) < original_length:
                self.save_to_json()  # Salvar alterações no arquivo JSON
                self.logger.info("Seletor removido com sucesso")
                return True

        self.logger.warning(f"Seletor ID {selector_id} não encontrado para remoção")
        return False

    def setup_logging(self):
        """Configura o logging para terminal e arquivo"""
        self.logger = logging.getLogger('selectors')
        self.logger.setLevel(logging.INFO)  # Mudado de DEBUG para INFO

        # Criar diretório de logs se não existir
        os.makedirs('logs', exist_ok=True)

        # Handler para arquivo
        file_handler = logging.FileHandler('logs/selectors.log')
        file_handler.setLevel(logging.DEBUG)

        # Handler para console
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.DEBUG)

        # Definir o formato do log
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Limpar handlers existentes e adicionar os novos
        self.logger.handlers = []
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

        # Evitar propagação para outros loggers
        self.logger.propagate = False

    def get_selector_type(self, selector_id):
        """Retorna o tipo do seletor baseado no ID"""
        for selector_type, selectors in self.selector_map.items():
            for selector in selectors:
                if selector['id'] == selector_id:
                    return selector_type
        return None

    def process_offer_type(self, flash_deal, bestseller, recommended):
        """
        Determina o tipo de oferta baseado nos critérios:
        - Oferta Relâmpago: se tiver tag de flash deal
        - Mais Vendido: se tiver tag de bestseller
        - Recomendado: se tiver tag de recomendado
        - Normal: caso padrão
        """
        if flash_deal:
            return "Oferta Relâmpago"
        if bestseller:
            return "Mais Vendido"
        if recommended:
            return "Recomendado"
        return "Normal"
    def get_status(self):
        """Retorna o status atual do gerenciador de seletores"""
        with self.lock:
            return self.current_status

    def set_status(self, status):
        """Atualiza o status do gerenciador de seletores"""
        with self.lock:
            self.current_status = status
            logger.info(f"Status do SelectorManager: {status}")

    def clean_product_url(self, url):
        """
        Limpa a URL do produto, removendo parâmetros de tracking e convertendo links de redirecionamento
        """
        if not url:
            return ""

        current_store = self.store_manager.get_current_store()

        # Tratamento específico para Mercado Livre
        if current_store == "MercadoLivre":
            # Se for um link de redirecionamento
            if "click1.mercadolivre.com.br/mclics/clicks/external" in url:
                try:
                    # Extrai o ID do produto do parâmetro wid
                    wid_match = re.search(r'wid=([^&]+)', url)
                    if wid_match:
                        product_id = wid_match.group(1)
                        # Constrói a URL direta do produto
                        return f"https://www.mercadolivre.com.br/p/{product_id}"
                except Exception as e:
                    self.logger.error(f"Erro ao processar URL de redirecionamento: {e}")
                    return url

            # Para URLs diretas, remove parâmetros desnecessários
            try:
                base_url = url.split('#')[0]
                if "/p/MLB" in base_url:
                    return base_url
            except Exception as e:
                self.logger.error(f"Erro ao limpar URL: {e}")

        # Tratamento específico para Magalu
        elif current_store == "Magalu":
            try:
                # Verificar se a URL está completa
                if not url.startswith("http"):
                    # Adicionar domínio base se necessário
                    if url.startswith("//"):
                        url = f"https:{url}"
                    elif url.startswith("/"):
                        url = f"https://www.magazinevoce.com.br{url}"
                    else:
                        url = f"https://www.magazinevoce.com.br/magazinepromobelloficial/{url}"

                # Remove parâmetros de tracking
                if "?" in url:
                    base_url = url.split('?')[0]
                    return base_url
                return url
            except Exception as e:
                self.logger.error(f"Erro ao limpar URL do Magalu: {e}")
                return url

        return url
