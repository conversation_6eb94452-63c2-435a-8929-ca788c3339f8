import tkinter as tk
from tkinter import ttk
import os
import sys
import logging
from datetime import datetime

from utils.scraper_process import ScraperProcess

# Garantir que os diretórios de componentes estejam no PYTHONPATH
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.category_manager import CategoryManager
from utils.selector_manager import SelectorManager
from utils.ai_manager import AIManager  
from utils.store_manager import StoreManager 
from ui.category_frame_new import CategoryFrame
from ui.execution_frame import ExecutionFrame
from ui.selector_frame import SelectorFrame
from ui.error_dialog import ErrorDialog
from src.scrapers.scraper import Scraper

class PromohunterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Promohunter")
        self.root.geometry("1000x600")
        self.root.minsize(800, 500)

        # Variáveis de status e progresso
        self.status_var = tk.StringVar()
        self.status_var.set("Pronto")
        self.progress_var = tk.DoubleVar()
        self.status_details = tk.StringVar()

        # Configuração de logging e tratamento de erros
        self.setup_error_handling()

        # Criar gerenciadores
        self.store_manager = StoreManager()  
        self.category_manager = CategoryManager(store_manager=self.store_manager)
        self.selector_manager = SelectorManager(store_manager=self.store_manager)
        self.ai_manager = AIManager()  
        self.scraper_process = ScraperProcess(ai_manager=self.ai_manager, store_manager=self.store_manager)
        self.scraper = Scraper(store_manager=self.store_manager) 

        try:
            self.initialize_managers()
        except Exception as e:
            self.handle_initialization_error(e)

        self.setup_ui()

    def setup_ui(self):
        # Frame principal com abas
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Aba de execução
        self.execution_frame = ExecutionFrame(
            notebook,
            self.scraper_process,
            self.category_manager,
            ai_manager=self.ai_manager,
            store_manager=self.store_manager,
            status_callback=self.update_status
        )
        notebook.add(self.execution_frame, text="Execução")

        # Aba de categorias
        self.category_frame = CategoryFrame(
            notebook,
            self.category_manager,
            self.update_status,
            store_manager=self.store_manager
        )
        notebook.add(self.category_frame, text="Categorias")

        # Aba de seletores
        self.selector_frame = SelectorFrame(
            notebook,
            self.selector_manager,
            self.update_status,
            store_manager=self.store_manager
        )
        notebook.add(self.selector_frame, text="Seletores CSS")


        # Barra de status
        status_bar = ttk.Label(
            self.root,
            textvariable=self.status_var,
            relief=tk.SUNKEN,
            anchor=tk.W
        )
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, padx=10, pady=5)

    def update_status(self, message):
        """Atualiza a mensagem de status"""
        self.status_var.set(message)

    def setup_error_handling(self):
        """Configura o sistema de tratamento de erros"""
        self.error_log = []
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/app.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def initialize_managers(self):
        """Inicializa todos os gerenciadores"""
        try:

            # Carregar categorias e seletores
            self.category_manager.load_from_json()
            self.selector_manager.validate_selectors()

            self.update_status("Todos os gerenciadores inicializados com sucesso")
        except Exception as e:
            self.logger.error(f"Erro na inicialização: {e}")
            raise

    def handle_initialization_error(self, error):
        """Trata erros de inicialização"""
        self.logger.error(f"Erro na inicialização: {error}")
        self.error_log.append({
            "timestamp": datetime.now().isoformat(),
            "error": str(error),
            "type": "initialization"
        })
        self.update_status(f"Erro na inicialização: {error}")
        self.show_error_dialog("Erro de Inicialização", str(error))

    def show_error_dialog(self, title, message):
        """Exibe diálogo de erro customizado"""
        dialog = ErrorDialog(self.root, title, message)
        dialog.show()

    def update_progress(self, value, message=""):
        """Atualiza a barra de progresso e mensagem"""
        self.progress_var.set(value)
        if message:
            self.status_details.set(message)

def main():
    root = tk.Tk()
    app = PromohunterApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
