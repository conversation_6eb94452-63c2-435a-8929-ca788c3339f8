class Product:
    def __init__(
        self,
        plataforma: str,
        url_produto: str,
        url_afiliado: str,
        url_imagem: str,
        titulo: str,
        categoria: str,
        categoria_id: int,     
        subcategoria: str,  
        subcategoria_id: int,  
        descricao: str, 
        preco_atual: float,
        preco_antigo: float,
        preco_alternativo: float,
        ativo: bool,
        cupom: str,
        menor_preco: bool,
        indicamos: bool,
        disparar_whatsapp: bool,
        grupo_whatsapp: str,
        frete: bool,
        invalidProduct: bool,
        isStory: bool
    ):
        self.plataforma = plataforma
        self.url_produto = url_produto
        self.url_afiliado = url_afiliado
        self.url_imagem = url_imagem
        self.titulo = titulo
        self.categoria = categoria
        self.categoria_id = categoria_id
        self.subcategoria = subcategoria
        self.subcategoria_id = subcategoria_id
        self.descricao = descricao
        self.preco_atual = preco_atual
        self.preco_antigo = preco_antigo
        self.preco_alternativo = preco_alternativo
        self.ativo = ativo
        self.cupom = cupom
        self.menor_preco = menor_preco
        self.indicamos = indicamos
        self.disparar_whatsapp = disparar_whatsapp
        self.grupo_whatsapp = grupo_whatsapp
        self.frete = frete
        self.invalidProduct = invalidProduct
        self.isStory = isStory

    def to_dict(self):
        return {
            "plataforma": self.plataforma,
            "url_produto": self.url_produto,
            "url_afiliado": self.url_afiliado,
            "url_imagem": self.url_imagem,
            "titulo": self.titulo,
            "categoria": self.categoria,
            "categoria_id": self.categoria_id,
            "subcategoria": self.subcategoria,
            "subcategoria_id": self.subcategoria_id,
            "descricao": self.descricao,
            "preco_atual": self.preco_atual,
            "preco_antigo": self.preco_antigo,
            "preco_alternativo": self.preco_alternativo,
            "ativo": self.ativo,
            "cupom": self.cupom,
            "menor_preco": self.menor_preco,
            "indicamos": self.indicamos,
            "disparar_whatsapp": self.disparar_whatsapp,
            "grupo_whatsapp": self.grupo_whatsapp,
            "frete": self.frete,
            "invalidProduct": self.invalidProduct,
            "isStory": self.isStory
        }

