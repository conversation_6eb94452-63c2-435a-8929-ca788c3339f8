class Product:
    def __init__(
        self,
        platform,
        url,
        title,
        price,
        old_price,
        image_url,
        installments=None,
        coupon_info=None,
    ):
        self.platform = platform
        self.url = url
        self.title = title
        self.price = price
        self.old_price = old_price
        self.image_url = image_url
        self.installments = installments
        self.coupon_info = coupon_info

    def to_dict(self):
        return {
            "platform": self.platform,
            "url": self.url,
            "title": self.title,
            "price": self.price,
            "old_price": self.old_price,
            "image_url": self.image_url,
            "installments": self.installments,
            "coupon_info": self.coupon_info,
        }
