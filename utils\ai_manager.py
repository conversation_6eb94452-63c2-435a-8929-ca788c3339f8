import json
import httpx
import os
import logging
from typing import Optional, Dict, Any
from openai import OpenAI
from google import genai
import asyncio
import re
import urllib.parse

try:
    openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
except Exception:
    openai_client = None
try:
    google_ai_client = genai.Client(api_key=os.getenv("GOOGLE_AI_STUDIO_KEY"))
except Exception:
    google_ai_client = None
class AIManager:
    MAX_RETRIES = 3
    RETRY_DELAY = 2  # segundos

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.current_api = "OpenRouter"  # API padrão
        self.setup_api_configs()
        self.categories = self._load_categories_promobell()
    def setup_api_configs(self):
        """Configura as APIs baseado na seleção atual"""
        if self.current_api == "OpenRouter":
            self.openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
            self.api_url = "https://openrouter.ai/api/v1/chat/completions"
            self.headers = {
                "Authorization": f"Bearer {self.openrouter_api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/Promobell/promohunter",
            }
            self.models = self._load_and_sort_models()
        elif self.current_api == "OpenAI":
            self.openai_api_key = os.getenv("OPENAI_API_KEY")
            if not self.openai_api_key:
                self.logger.error("OPENAI_API_KEY não encontrada nas variáveis de ambiente")
            self.openai_client = OpenAI(api_key=self.openai_api_key)
        elif self.current_api == "GoogleAI":
            self.google_ai_key = os.getenv("GOOGLE_AI_STUDIO_KEY")
            if not self.google_ai_key:
                self.logger.error("GOOGLE_AI_STUDIO_KEY não encontrada nas variáveis de ambiente")
            self.google_ai_client = genai.Client(api_key=self.google_ai_key)
        else:
            self.logger.error(f"API não reconhecida: {self.current_api}")


    def _load_categories_promobell(self) -> Dict:
        """Carrega as categorias do arquivo JSON"""
        try:
            categories_path = os.path.join("data", "categories_promobell.json")
            with open(categories_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"Erro ao carregar categorias: {e}")
            return {}
    def get_category_name(self, category_id: str) -> str:
        """Retorna o nome da categoria pelo ID"""
        return self.categories.get(str(category_id), {}).get("name", "")

    def get_subcategories(self, category_id: str) -> list:
        """Retorna as subcategorias de uma categoria"""
        return self.categories.get(str(category_id), {}).get("subcategories", [])
    def set_api(self, api_name: str) -> bool:
        """Define qual API será utilizada"""
        try:
            self.current_api = api_name
            self.setup_api_configs()
            self.logger.info(f"Usando API: {api_name}")
            return True
        except Exception as e:
            self.logger.error(f"Erro ao configurar API {api_name}: {e}")
            return False

    def _load_and_sort_models(self) -> list:
        """Carrega e ordena os modelos por throughput"""
        try:
            with open("data/llm_models.json", 'r') as f:
                data = json.load(f)
                # Converte throughput para float e ordena em ordem decrescente
                sorted_models = sorted(
                    data['models'],
                    key=lambda x: float(x['throughput']),
                    reverse=True
                )
                return sorted_models
        except Exception as e:
            self.logger.error(f"Erro ao carregar modelos: {e}")
            return []
    async def _make_request_openRouter(self, messages: list) -> Optional[Dict[str, Any]]:
        """Faz requisições para a API do OpenRouter com fallback para outros modelos"""
        if not self.models:
            self.logger.error("Nenhum modelo disponível")
            return None

        for model in self.models:
            try:
                self.current_model = model['name']
                self.logger.debug(f"Tentando modelo: {model['name']} (throughput: {model['throughput']})")

                payload = {
                    "model": model['name'],
                    "messages": messages,
                    "temperature": 0.7,
                    "max_tokens": 50,
                }

                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        self.api_url,
                        headers=self.headers,
                        json=payload,
                        timeout=10.0
                    )

                    # Verifica se a resposta foi bem-sucedida
                    if response.status_code == 200:
                        data = response.json()

                        # Verifica se a resposta contém o conteúdo esperado
                        if (data.get('choices') and
                            len(data['choices']) > 0 and
                            data['choices'][0].get('message', {}).get('content')):

                            content = data['choices'][0]['message']['content'].strip()

                            # Verifica se o conteúdo não está vazio
                            if content and len(content) > 0:
                                self.logger.info(f"Sucesso com modelo: {model['name']}")
                                return data

                    self.logger.warning(f"Resposta inválida do modelo {model['name']}, tentando próximo modelo")

            except httpx.HTTPError as e:
                self.logger.warning(f"Erro HTTP com modelo {model['name']}: {e}")
                continue
            except Exception as e:
                self.logger.warning(f"Erro com modelo {model['name']}: {e}")
                continue

        self.logger.error("Todos os modelos falharam")
        return None
    async def _make_request_openAI(self, messages: list) -> Optional[Dict[str, Any]]:
        """Faz uma requisição para a API da OpenAI usando a biblioteca oficial"""
        try:
            response = openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=messages,
                temperature=0.7,
                max_tokens=50,
                top_p=1.0,
                frequency_penalty=0,
                presence_penalty=0
            )
            return response
        except Exception as e:
            self.logger.error(f"Erro na requisição OpenAI: {str(e)}")
            return None
    # async def generate_description_openRouter(self, product_name: str) -> str:
    #     """Gera uma descrição criativa para o produto"""
    #     try:
    #         messages = [
    #             {
    #                 "role": "user",
    #                 "content": f"Você é um assistente especialista em criar frases promocionais criativas e persuasivas. Crie uma frase impactante com, no máximo, dez palavras. A frase deve começar com um emoji e destacar de forma criativa as principais características do produto '{product_name}'. Nunca use emojis no final, apenas no início."
    #             }
    #         ]

    #         response = await self._make_request_openRouter(messages)
    #         if response and 'choices' in response and len(response['choices']) > 0:
    #             content = response['choices'][0]['message']['content']
    #             clean_content = content.strip('*').strip()
    #             return clean_content
    #         return ""

    #     except Exception as e:
    #         self.logger.error(f"Erro na geração de descrição: {e}")
    #         return ""

    async def get_category_openRouter(self, product_name: str) -> str:
        """Determina a categoria do produto"""
        try:
            # categories_text = "\n".join([f"{k}. {v[0]}" for k, v in self.categories.items()])
            categories_text = "\n".join([f"{k}. {v['name']}" for k, v in self.categories.items()])

            messages = [
                {
                    "role": "user",
                    "content": f"Você é um especialista em categorização de produtos. Categorize este produto: {product_name}\n\nCategorias disponíveis:\n{categories_text}\n\nResponda APENAS com o nome da categoria, não retorne outro texto."
                }
            ]

            response = await self._make_request_openRouter(messages)
            if response and 'choices' in response and len(response['choices']) > 0:
                content = response['choices'][0]['message']['content']
                clean_content = content.strip('*').strip().split('\n')[0]

                # Verifica se a categoria existe
                for cat_id, cat_info in self.categories.items():
                    if cat_info['name'] in clean_content:
                        return cat_info['name']

                return ""

        except Exception as e:
            self.logger.error(f"Erro na categorização: {e}")
            return ""
    async def get_subcategory_openRouter(self, product_name: str, category: str) -> str:
        """Determina a subcategoria do produto"""
        try:
            # Encontra o ID da categoria pelo nome
            category_id = next((k for k, v in self.categories.items() if v['name'] == category), "1")

            subcategories = self.categories[category_id]['subcategories']
            subcategories_text = "\n".join(subcategories)

            messages = [
                {
                    "role": "user",
                    "content": f"Você é um especialista em categorização e subcategorização de produtos. De acordo com a categoria {category}, escolha a subcategoria para: {product_name}\n\nSubcategorias disponíveis:\n{subcategories_text}\n\nResponda APENAS com o nome da subcategoria, não retorne outro texto."
                }
            ]

            response = await self._make_request_openRouter(messages)
            if response and 'choices' in response and len(response['choices']) > 0:
                content = response['choices'][0]['message']['content'].strip()
                if content in subcategories:
                    return content
            return ""

        except Exception as e:
            self.logger.error(f"Erro na subcategorização: {e}")
            return ""
    # async def generate_description_openAI(self, product_name: str) -> str:
    #     """Gera uma descrição criativa para o produto"""
    #     try:
    #         messages = [
    #             {
    #                 "role": "user",
    #                 "content": f"Você é um assistente especialista em criar frases promocionais criativas e persuasivas. Crie uma frase impactante com, no máximo, dez palavras. A frase deve começar com um emoji e destacar de forma criativa as principais características do produto '{product_name}'. Nunca use emojis no final, apenas no início."
    #             }
    #         ]

    #         response = await self._make_request_openAI(messages)
    #         if response and hasattr(response, 'choices') and response.choices:
    #             return response.choices[0].message.content.strip().strip('"')
    #         return ""

    #     except Exception as e:
    #         self.logger.error(f"Erro na geração de descrição: {e}")
    #         return ""

    async def get_category_openAI(self, product_name: str) -> str:
        """Determina a categoria do produto"""
        try:
            categories_text = "\n".join([f"{k}. {v['name']}" for k, v in self.categories.items()])

            messages = [
                {
                    "role": "user",
                    "content": f"Você é um especialista em categorização de produtos. Categorize este produto: {product_name}\n\nCategorias disponíveis:\n{categories_text}\n\nResponda APENAS com o nome da categoria, não retorne outro texto."
                }
            ]

            response = await self._make_request_openAI(messages)
            if response and hasattr(response, 'choices') and response.choices:
                content = response.choices[0].message.content.strip()

                # Verifica se a categoria existe
                for cat_id, cat_info in self.categories.items():
                    if cat_info['name'] in content:
                        return cat_info['name']

                return ""

        except Exception as e:
            self.logger.error(f"Erro na categorização: {e}")
            return ""

    async def get_subcategory_openAI(self, product_name: str, category: str) -> tuple:
        """Determina a subcategoria do produto e retorna (subcategoria, subcategoria_id)"""
        try:
            # Encontra o ID da categoria pelo nome
            category_id = next((k for k, v in self.categories.items() if v['name'] == category), "1")

            subcategories = self.categories[category_id]['subcategories']
            subcategories_text = "\n".join(subcategories)

            messages = [
                {
                    "role": "user",
                    "content": f"Você é um especialista em categorização e subcategorização de produtos. De acordo com a categoria {category}, escolha a subcategoria para: {product_name}\n\nSubcategorias disponíveis:\n{subcategories_text}\n\nResponda APENAS com o nome da subcategoria, não retorne outro texto."
                }
            ]

            response = await self._make_request_openAI(messages)
            if response and hasattr(response, 'choices') and response.choices:
                content = response.choices[0].message.content.strip()
                if content in subcategories:
                    for i, subcategory in enumerate(subcategories, 1):
                        if subcategory in content:
                            return content, i
            return "", 0

        except Exception as e:
            self.logger.error(f"Erro na subcategorização: {e}")
            return "", 0

    async def _make_request_googleAI(self, prompt: str) -> Optional[str]:
        """Faz uma requisição para a API do Google AI Studio"""
        if self.current_api != "GoogleAI":
            return None

        try:
            response = google_ai_client.models.generate_content(
                model="gemini-2.5-flash-preview-04-17",
                contents=prompt,
            )
            self.logger.info(f"Resposta completa da API Google AI: {response}")  # Log detalhado adicionado
            # Se a resposta for um objeto ou dicionarizado, verifique o status
            if hasattr(response, 'status_code') and response.status_code == 429:
                raise Exception("429 RESOURCE_EXHAUSTED")
            if response and hasattr(response, 'text'):
                text = response.text.strip()
                if not text:
                    self.logger.warning(f"Resposta da API Google AI vazia: {response}")
                    raise Exception("Resposta vazia da API Google AI")
                return text
            else:
                self.logger.warning(f"Resposta inesperada da API Google AI: {response}")
            return None
        except Exception as e:
            self.logger.error(f"Erro na requisição Google AI: {str(e)}")
            raise

    # async def generate_description_googleAI(self, product_name: str) -> str:
    #     """Gera uma descrição criativa para o produto usando Google AI Studio"""
    #     try:
    #         prompt = (
    #             f"Você é um assistente especialista em criar frases promocionais criativas e persuasivas. "
    #             f"Crie uma frase impactante com, no máximo, dez palavras. A frase deve começar com um emoji "
    #             f"e destacar de forma criativa as principais características do produto '{product_name}'. "
    #             f"Nunca use emojis no final, apenas no início."
    #         )

    #         response = await self._make_request_googleAI(prompt)
    #         return response if response else ""

    #     except Exception as e:
    #         self.logger.error(f"Erro na geração de descrição (Google AI): {e}")
    #         return ""

    async def _make_request_with_retry(self, request_func, *args):
        """Wrapper para adicionar retry às requisições com espera exponencial"""
        # Parâmetros para backoff exponencial
        initial_delay = 2  # segundos
        max_delay = 30     # segundos
        delay = initial_delay

        for attempt in range(self.MAX_RETRIES):
            try:
                self.logger.info(f"Tentativa {attempt + 1}/{self.MAX_RETRIES} para {request_func.__name__}")

                # Chama a função de requisição
                result = await request_func(*args)

                # Verifica se o resultado é válido - adaptado para lidar com tuplas
                if result:
                    if isinstance(result, tuple) and len(result) >= 1:
                        # Se for uma tupla, verifica o primeiro elemento
                        if result[0] or isinstance(result[0], int):
                            self.logger.info(f"Requisição bem-sucedida na tentativa {attempt + 1}")
                            return result
                    elif isinstance(result, str) and result.strip():
                        self.logger.info(f"Requisição bem-sucedida na tentativa {attempt + 1}")
                        return result

                self.logger.warning(f"Resultado vazio ou inválido na tentativa {attempt + 1}")
                # Se não for a última tentativa, trata como erro e tenta novamente
                if attempt < self.MAX_RETRIES - 1:
                    raise Exception("Resultado vazio ou inválido")

            except Exception as e:
                error_msg = str(e)

                # Verifica se é um erro de quota (429)
                is_quota_error = "429" in error_msg or "RESOURCE_EXHAUSTED" in error_msg

                # Se não for a última tentativa e for um erro de quota, tenta novamente
                if attempt < self.MAX_RETRIES - 1 and (is_quota_error or "vazio" in error_msg.lower()):
                    wait_time = delay

                    self.logger.warning(
                        f"Erro na tentativa {attempt + 1}: {error_msg}. "
                        f"Aguardando {wait_time}s antes da próxima tentativa..."
                    )

                    # Espera antes da próxima tentativa
                    await asyncio.sleep(wait_time)

                    # Aumenta o delay para a próxima tentativa (backoff exponencial)
                    delay = min(delay * 2, max_delay)
                else:
                    # Se for a última tentativa, registra o erro
                    self.logger.error(f"Todas as tentativas falharam para {request_func.__name__}: {error_msg}")

                    # Retorna valores padrão baseados na função chamada
                    if request_func.__name__ == "_get_category_googleAI_internal":
                        return "Tudo Tech", 3
                    elif request_func.__name__ == "_get_subcategory_googleAI_internal":
                        return "Smartphones", 1
                    else:
                        return "Indefinido"

        # Se chegou aqui, todas as tentativas falharam
        self.logger.error(f"Retornando valor padrão após {self.MAX_RETRIES} tentativas")

        # Retorna valores padrão baseados na função chamada
        if request_func.__name__ == "_get_category_googleAI_internal":
            return "Tudo Tech", 3
        elif request_func.__name__ == "_get_subcategory_googleAI_internal":
            return "Smartphones", 1
        else:
            return "Indefinido"

    async def get_category_googleAI(self, product_name: str) -> tuple:
        """Determina a categoria do produto usando Google AI"""
        self.logger.info(f"Obtendo categoria para: {product_name}")
        try:
            result, category_id = await self._make_request_with_retry(self._get_category_googleAI_internal, product_name)
            self.logger.info(f"Categoria obtida: {result}, ID: {category_id}")
            return result, category_id
        except Exception as e:
            self.logger.error(f"Erro ao obter categoria: {e}")
            return "", 1

    async def get_subcategory_googleAI(self, product_name: str, category: str) -> tuple:
        """Determina a subcategoria do produto usando Google AI"""
        self.logger.info(f"Obtendo subcategoria para: {product_name} (categoria: {category})")
        try:
            subcategory, subcategory_id = await self._make_request_with_retry(self._get_subcategory_googleAI_internal, product_name, category)
            self.logger.info(f"Subcategoria obtida: {subcategory}, ID: {subcategory_id}")
            return subcategory, subcategory_id
        except Exception as e:
            self.logger.error(f"Erro ao obter subcategoria: {e}")
            return "Smartphones", 1  # Valor padrão

    async def _get_category_googleAI_internal(self, product_name: str) -> tuple:
        """Determina a categoria do produto usando Google AI Studio e retorna (categoria, categoria_id)"""
        try:
            categories_text = "\n".join([f"{k}. {v['name']}" for k, v in self.categories.items()])

            prompt = (
                f"Você é um especialista em categorização de produtos. "
                f"Categorize este produto: {product_name}\n\n"
                f"Categorias disponíveis:\n{categories_text}\n\n"
                f"Responda APENAS com o nome da categoria, não retorne outro texto."
            )

            response = await self._make_request_googleAI(prompt)
            if response:
                # Verifica se a categoria existe e retorna com o ID
                for cat_id, cat_info in self.categories.items():
                    if cat_info['name'].lower() in response.lower():
                        return cat_info['name'], int(cat_id)

            # Se não encontrou correspondência exata, tenta encontrar a mais próxima
            self.logger.info(f"Resposta da API: '{response}'. Tentando encontrar categoria mais próxima.")
            return "Tudo Tech", 3


        except Exception as e:
            self.logger.error(f"Erro na categorização (Google AI): {e}")
            return "", 1

    async def _get_subcategory_googleAI_internal(self, product_name: str, category: str) -> tuple:
        """Determina a subcategoria do produto usando Google AI Studio e retorna (subcategoria, subcategoria_id)"""
        try:
            category_id = next((k for k, v in self.categories.items() if v['name'] == category), "3")
            subcategories = self.categories[category_id]['subcategories']
            subcategories_text = "\n".join(subcategories)

            prompt = (
                f"Você é um especialista em categorização e subcategorização de produtos. "
                f"De acordo com a categoria {category}, escolha a subcategoria para: {product_name}\n\n"
                f"Subcategorias disponíveis:\n{subcategories_text}\n\n"
                f"Responda APENAS com o nome da subcategoria, não retorne outro texto."
            )

            response = await self._make_request_googleAI(prompt)
            if response:
                for i, subcategory in enumerate(subcategories, 1):
                    if subcategory.lower() in response.lower():
                        return subcategory, i

            # Se não encontrou correspondência, use a primeira subcategoria como padrão
            if subcategories and len(subcategories) > 0:
                return subcategories[0], 1

            return "Smartphones", 1  # Valor padrão

        except Exception as e:
            self.logger.error(f"Erro na subcategorização (Google AI): {e}")
            return "Smartphones", 1  # Valor padrão

    async def get_category(self, product_name: str) -> tuple:
        """Determina a categoria do produto e retorna (categoria, categoria_id)"""
        # Regras específicas para celulares
        if "celular" in product_name.lower() or "smartphone" in product_name.lower() or "galaxy" in product_name.lower() or "iphone" in product_name.lower():
            return "Vibe Mobile", 4

        try:
            if self.current_api == "GoogleAI":
                return await self.get_category_googleAI(product_name)
            elif self.current_api == "OpenAI":
                category = await self.get_category_openAI(product_name)
                # Encontra o ID da categoria pelo nome
                category_id = next((int(k) for k, v in self.categories.items() if v['name'] == category), 3)
                return category, category_id
            else:
                category = await self.get_category_openRouter(product_name)
                # Encontra o ID da categoria pelo nome
                category_id = next((int(k) for k, v in self.categories.items() if v['name'] == category), 3)
                return category, category_id
        except Exception as e:
            self.logger.error(f"Erro ao obter categoria: {e}")
            return "Tudo Tech", 3  # Categoria padrão

    async def get_subcategory(self, product_name: str, category: str) -> tuple:
        """Determina a subcategoria do produto e retorna (subcategoria, subcategoria_id)"""
        # Regras específicas para celulares
        if "celular" in product_name.lower() or "smartphone" in product_name.lower() or "galaxy" in product_name.lower() or "iphone" in product_name.lower():
            return "Smartphones", 1

        try:
            if self.current_api == "GoogleAI":
                return await self.get_subcategory_googleAI(product_name, category)
            elif self.current_api == "OpenAI":
                return await self.get_subcategory_openAI(product_name, category)
            else:
                subcategory = await self.get_subcategory_openRouter(product_name, category)
                # Encontra o ID da categoria pelo nome
                category_id = next((k for k, v in self.categories.items() if v['name'] == category), "3")
                # Encontra o ID da subcategoria
                subcategories = self.categories[category_id]['subcategories']
                subcategory_id = next((i for i, sub in enumerate(subcategories, 1) if sub == subcategory), 1)
                return subcategory, subcategory_id
        except Exception as e:
            self.logger.error(f"Erro ao obter subcategoria: {e}")
            return "Mais produtos relacionados", 1  # Subcategoria padrão

    def clean_product_url(self, url):
        """Limpa a URL do produto removendo parâmetros de rastreamento e redirecionamento"""
        try:
            # Verifica se é uma URL de redirecionamento do Mercado Livre
            if "click1.mercadolivre.com.br" in url or "click.mercadolivre.com.br" in url:
                # Extrai a URL real do parâmetro 'url='
                match = re.search(r'url=([^&]+)', url)
                if match:
                    # Decodifica a URL
                    decoded_url = urllib.parse.unquote(match.group(1))
                    # Limpa a URL decodificada
                    return self.clean_product_url(decoded_url)

            # Remove parâmetros de rastreamento comuns
            parsed_url = urllib.parse.urlparse(url)

            # Lista de parâmetros a manter (vazia = remover todos)
            params_to_keep = []

            if parsed_url.query:
                # Filtra apenas os parâmetros que queremos manter
                query_params = urllib.parse.parse_qs(parsed_url.query)
                filtered_params = {k: v for k, v in query_params.items() if k in params_to_keep}

                # Reconstrói a query string apenas com os parâmetros desejados
                new_query = urllib.parse.urlencode(filtered_params, doseq=True) if filtered_params else ""

                # Reconstrói a URL sem os parâmetros indesejados
                clean_url = urllib.parse.urlunparse((
                    parsed_url.scheme,
                    parsed_url.netloc,
                    parsed_url.path,
                    parsed_url.params,
                    new_query,
                    ""  # Remove o fragment (parte após #)
                ))

                return clean_url

            # Se não tiver query params, remove apenas o fragment
            return url.split('#')[0]

        except Exception as e:
            self.logger.error(f"Erro ao limpar URL: {e}")
            return url


