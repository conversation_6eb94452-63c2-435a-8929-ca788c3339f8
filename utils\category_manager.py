import json
import os
from utils.store_manager import StoreManager


class CategoryManager:
    def __init__(self, json_file="categories.json", store_manager=None):
        self.json_file = json_file
        self.store_manager = store_manager if store_manager else StoreManager()
        self.categories = {}
        self.load_from_json()

    def load_from_json(self):
        """Carrega categorias do arquivo JSON"""
        try:
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                    # Verificar se o formato é o novo (com lojas) ou o antigo
                    if isinstance(data, dict):
                        # Novo formato com lojas
                        self.categories = data
                    else:
                        # Formato antigo (lista de categorias)
                        # Converter para o novo formato
                        self.categories = {"MercadoLivre": data}

                    # Validar URLs
                    for store, categories in self.categories.items():
                        for category in categories:
                            if not all(key in category for key in ['id', 'name', 'url_template', 'max_page']):
                                raise ValueError(f"Categoria inválida: {category}")
                            if not category['url_template'].startswith(('http://', 'https://')):
                                raise ValueError(f"URL inválida para categoria {category['name']}")
            else:
                # Arquivo não existe, inicializar com estrutura vazia
                self.categories = {
                    "MercadoLivre": [],
                    "Magalu": []
                }
                self.save_to_json()
        except FileNotFoundError:
            print(f"Arquivo {self.json_file} não encontrado")
            self.categories = {"MercadoLivre": [], "Magalu": []}
        except json.JSONDecodeError:
            print(f"Erro ao decodificar {self.json_file}")
            self.categories = {"MercadoLivre": [], "Magalu": []}
        except Exception as e:
            print(f"Erro ao carregar categorias: {e}")
            self.categories = {"MercadoLivre": [], "Magalu": []}

    def get_all_categories(self):
        """Retorna todas as categorias da loja atual"""
        current_store = self.store_manager.get_current_store()
        if current_store not in self.categories:
            self.categories[current_store] = []
        return self.categories[current_store]

    def get_category(self, category_id):
        """Retorna uma categoria específica pelo ID"""
        current_store = self.store_manager.get_current_store()
        if current_store not in self.categories:
            return None

        for category in self.categories[current_store]:
            if category["id"] == int(category_id):
                return (category["id"], category["name"], category["url_template"], category["max_page"])
        return None

    def update_category(self, category_id, name, url_template, max_page):
        """Atualiza uma categoria existente"""
        current_store = self.store_manager.get_current_store()
        if current_store not in self.categories:
            return False

        category_id = int(category_id)
        for i, category in enumerate(self.categories[current_store]):
            if category["id"] == category_id:
                self.categories[current_store][i] = {
                    "id": category_id,
                    "name": name,
                    "url_template": url_template,
                    "max_page": max_page
                }
                self.save_to_json()
                return True
        return False

    def delete_category(self, category_id):
        """Remove uma categoria do JSON"""
        current_store = self.store_manager.get_current_store()
        if current_store not in self.categories:
            return False

        category_id = int(category_id)
        self.categories[current_store] = [cat for cat in self.categories[current_store] if cat["id"] != category_id]
        self.save_to_json()
        return True

    def delete_multiple_categories(self, category_ids):
        """Remove múltiplas categorias do JSON"""
        current_store = self.store_manager.get_current_store()
        if current_store not in self.categories:
            return False

        # Converter todos os IDs para inteiros
        category_ids = [int(cat_id) for cat_id in category_ids]

        # Filtrar as categorias que não estão na lista de IDs para excluir
        self.categories[current_store] = [cat for cat in self.categories[current_store]
                                         if cat["id"] not in category_ids]
        self.save_to_json()
        return True

    def save_to_json(self):
        """Salva as categorias no arquivo JSON"""
        with open(self.json_file, "w", encoding="utf-8") as f:
            json.dump(self.categories, f, indent=4, ensure_ascii=False)

    def add_category(self, name, url_template, max_page):
        """Adiciona uma nova categoria"""
        current_store = self.store_manager.get_current_store()
        if current_store not in self.categories:
            self.categories[current_store] = []

        # Verificar se já existe uma categoria com o mesmo nome
        if any(cat["name"] == name for cat in self.categories[current_store]):
            return False

        new_id = max([cat["id"] for cat in self.categories[current_store]], default=0) + 1
        new_category = {
            "id": new_id,
            "name": name,
            "url_template": url_template,
            "max_page": max_page
        }
        self.categories[current_store].append(new_category)
        self.save_to_json()
        return True
