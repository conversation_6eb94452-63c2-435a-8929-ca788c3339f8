import scrapy
from scrapy import Request
from bs4 import BeautifulSoup
import asyncio
from utils.ai_manager import AIManager
from twisted.internet import defer
import logging
import json

class MlSpider(scrapy.Spider):
    name = 'ml'
    allowed_domains = ["mercadolivre.com.br"]
    
    custom_settings = {
        "DOWNLOAD_DELAY": 3,
        "RANDOMIZE_DOWNLOAD_DELAY": True,
        "REDIRECT_ENABLED": True,
        "REDIRECT_MAX_TIMES": 5,
        "LOG_FORMAT": "%(message)s"  # Simplifica o formato do log
    }
    
    def __init__(self, category_info=None, *args, **kwargs):
        super(MlSpider, self).__init__(*args, **kwargs)
        self.logger.setLevel(logging.DEBUG)
        self.category_info = category_info
        self.products_processed = 0
        self.pages_processed = 0
        
        self.logger.info("Iniciando spider para categoria: %s", 
            self.category_info.get('name') if self.category_info else 'Unknown')
        
        if not self.category_info:
            self.logger.error("Category info não fornecida")
            return
            
        if not isinstance(self.category_info, dict):
            self.logger.error("Category info inválida: %s", type(self.category_info))
            return
            
        self.logger.info("Configuração carregada: %s", 
            json.dumps(self.category_info, ensure_ascii=False))

    def start_requests(self):
        if not self.category_info:
            self.logger.error("Não é possível iniciar: category_info não fornecida")
            return
        for url in self.start_urls:
            yield scrapy.Request(
                    url=url,
                    callback=self.parse,
                    dont_filter=True,
                    meta={'log_headers': True}  
                    )
        
        url_template = self.category_info.get('url_template')
        max_page = int(self.category_info.get('max_page', 1))
        
        self.logger.info("Processando categoria '%s' - %d páginas", 
            self.category_info['name'], max_page)

        for page in range(1, max_page + 1):
            url = url_template.format(i=page)
            self.logger.info("Processando página %d: %s", page, url)
            
            yield Request(
                url=url,
                callback=self.parse,
                errback=self.errback_httpbin,
                meta={'page': page},
                dont_filter=True
            )

    def parse(self, response):
        if response.meta.get('log_headers'):
            self.logger.info(f"Headers used: {response.request.headers}")        
        page = response.meta.get('page', 'Unknown')
        self.logger.info("Recebida página %s - Status: %s", page, response.status)

        products = response.css('div.ui-search-result__wrapper')
        
        if not products:
            self.logger.warning("Nenhum produto encontrado na página %s", page)
            self.logger.debug("URL: %s", response.url)
            self.logger.debug("Status: %s", response.status)
            return

        self.logger.info("Encontrados %d produtos na página %s", len(products), page)

        for idx, product in enumerate(products, 1):
            try:
                title = product.css('h2.ui-search-item__title::text').get()
                url = product.css('a.ui-search-item__group__element::attr(href)').get()
                
                if not title or not url:
                    self.logger.warning("Produto %d: Dados incompletos", idx)
                    continue

                self.logger.info("Processando produto: %s", title)
                self.products_processed += 1
                
                yield {
                    'title': title,
                    'url': url,
                    'page': page,
                    'position': idx
                }
                
            except Exception as e:
                self.logger.error("Erro ao processar produto %d: %s", idx, str(e))

        self.pages_processed += 1
        self.logger.info("Página %s concluída. Progresso: %d produtos, %d páginas", 
            page, self.products_processed, self.pages_processed)

    def errback_httpbin(self, failure):
        self.logger.error("Falha na requisição: %s", str(failure.value))
        self.logger.error("URL: %s", failure.request.url)

    def closed(self, reason):
        self.logger.info("Spider finalizado: %s", reason)
        self.logger.info("Resumo: %d páginas, %d produtos", 
            self.pages_processed, self.products_processed)
