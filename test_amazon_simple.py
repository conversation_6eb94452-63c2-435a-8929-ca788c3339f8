#!/usr/bin/env python3
"""
Teste simples para verificar se o scraper da Amazon está funcionando
"""

import requests
import time

def test_amazon_simple():
    """Teste simples da URL da Amazon"""
    
    url = "https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-tools&promotionsSearchStartIndex=30&promotionsSearchPageSize=90"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    }
    
    try:
        print("🔍 Testando URL da Amazon...")
        print(f"URL: {url}")
        
        response = requests.get(url, headers=headers, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Tamanho da resposta: {len(response.text)} caracteres")
        
        if response.status_code == 200:
            # Verificar se há bloqueio
            content_lower = response.text.lower()
            if 'robot' in content_lower or 'captcha' in content_lower:
                print("⚠️ Amazon detectou bot - bloqueio ativo")
                return False
            else:
                print("✅ Sem bloqueio detectado")
                
                # Verificar se há produtos
                if 'data-asin' in response.text:
                    print("✅ Produtos encontrados (data-asin presente)")
                    return True
                else:
                    print("❌ Nenhum produto encontrado")
                    return False
        else:
            print(f"❌ Erro HTTP: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

if __name__ == "__main__":
    success = test_amazon_simple()
    if success:
        print("\n✅ Teste passou - Amazon acessível")
    else:
        print("\n❌ Teste falhou - Problemas com Amazon")
