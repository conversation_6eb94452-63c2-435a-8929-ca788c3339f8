"""
Adaptador para o scraper da Amazon usando Scrapy e métodos alternativos
"""

import logging
import random
import time
import os
import sys
import json
from typing import Optional
import asyncio
try:
    import aiohttp
except ImportError:
    logging.error("Módulo aiohttp não encontrado. Execute 'pip install aiohttp' para instalar.")
    aiohttp = None
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Removido dependências do Scrapy - usando apenas requests/aiohttp e BeautifulSoup
from src.scrapers.base_scraper import BaseScraper
from utils.header_manager import HeaderManager

# Configurar logger
logger = logging.getLogger(__name__)

# Definir project_root
script_dir = os.path.dirname(os.path.abspath(__file__))
# Subir dois níveis: src/scrapers -> src -> raiz do projeto
project_root = os.path.dirname(os.path.dirname(script_dir))

# Adicionar project_root ao sys.path se ainda não estiver
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    logger.info(f"Adicionado {project_root} ao sys.path")

class AmazonScraper(BaseScraper):
    """
    Scraper para a Amazon com múltiplas estratégias de raspagem
    """

    def __init__(self, log_callback=None):
        super().__init__()
        self.store_id = "amazon"
        self.name = "Amazon"
        self.base_url = "https://www.amazon.com.br"
        self.deals_url = "https://www.amazon.com.br/deals"
        self.scrapy_available = True
        self.header_manager = HeaderManager()
        self._is_running = False
        self.current_status = "Não iniciado"
        self.log_callback = log_callback

        # Definir os seletores CSS para a Amazon
        self.selectors = {
            'product_container': 'div.a-section.a-spacing-base',
            'product_title': 'span.a-size-base-plus.a-color-base.a-text-normal',
            'product_price': 'span.a-price-whole',
            'product_old_price': 'span.a-price.a-text-price span.a-offscreen',
            'product_link': 'a.a-link-normal.a-text-normal',
            'product_image': 'img.s-image',
            'deal_badge': 'span.a-badge-text',
            'next_page': 'a.s-pagination-next'
        }

        # Configurações de delay para evitar bloqueio
        self.delay_min = 2.0
        self.delay_max = 5.0

        logger.info("AmazonScraper inicializado com múltiplas estratégias")

    def set_status(self, status):
        """Atualiza o status atual do scraper"""
        self.current_status = status
        if self.log_callback: # Envia status para o callback
            self.log_callback(f"Status do AmazonScraper: {status}")
        logger.info(f"Status do AmazonScraper: {status}")

    def get_status(self):
        """Retorna o status atual do scraper"""
        return self.current_status

    def _add_random_delay(self):
        """Adiciona um delay aleatório entre requisições para evitar detecção"""
        delay = random.uniform(self.delay_min, self.delay_max)
        if self.log_callback: # Envia debug para o callback
            self.log_callback(f"Aguardando {delay:.2f} segundos antes da próxima requisição")
        logger.debug(f"Aguardando {delay:.2f} segundos antes da próxima requisição")

    async def _get_html_with_browser_simulation(self, url: str) -> Optional[str]:
        """
        Obtém o HTML usando simulação de navegador com rotação de headers e delays
        """
        try:
            # Usar o HeaderManager para obter headers aleatórios
            headers = self.header_manager.get_random_header() if hasattr(self, 'header_manager') else None

            # Adicionar delay aleatório para evitar detecção
            await asyncio.sleep(random.uniform(3.0, 7.0))

            # Configurar timeout mais longo
            timeout = aiohttp.ClientTimeout(total=30)

            async with aiohttp.ClientSession(headers=headers, timeout=timeout) as session:
                async with session.get(url, ssl=False) as response:
                    if response.status == 200:
                        return await response.text()
                    else:
                        error_msg = f"Erro ao acessar a URL {url}: {response.status} {response.reason}"
                        logger.error(error_msg)
                        if self.log_callback:
                            self.log_callback(error_msg)
                        return None
        except Exception as e:
            error_msg = f"Erro ao obter HTML com simulação de navegador: {e}"
            logger.error(error_msg)
            if self.log_callback:
                self.log_callback(error_msg)
            return None

    async def extract_product_data(self, product_element, base_url):
        """Extrai dados de um elemento de produto"""
        try:
            # Extrai título
            title_element = product_element.select_one(self.selectors['product_title'])
            title = title_element.text.strip() if title_element else "Sem título"

            # Extrai preço atual
            price_element = product_element.select_one(self.selectors['product_price'])
            price_text = price_element.text.strip() if price_element else "0"
            price_text = price_text.replace(".", "").replace(",", ".").replace("R$", "").strip()
            try:
                price = float(price_text)
            except ValueError:
                price = 0.0

            # Extrai preço antigo
            old_price_element = product_element.select_one(self.selectors['product_old_price'])
            old_price_text = old_price_element.text.strip() if old_price_element else "0"
            old_price_text = old_price_text.replace(".", "").replace(",", ".").replace("R$", "").strip()
            try:
                old_price = float(old_price_text)
            except ValueError:
                old_price = 0.0

            # Extrai link
            link_element = product_element.select_one(self.selectors['product_link'])
            link = link_element.get('href') if link_element else ""
            if link and not link.startswith('http'):
                link = urljoin(base_url, link)

            # Extrai imagem
            image_element = product_element.select_one(self.selectors['product_image'])
            image_url = image_element.get('src') if image_element else ""

            # Verifica se é uma oferta
            deal_badge = product_element.select_one(self.selectors['deal_badge'])
            is_deal = bool(deal_badge)

            # Calcula desconto
            discount = 0
            if old_price > 0 and price > 0:
                discount = round(100 - (price * 100 / old_price))

            # Obtém categoria usando IA
            category = ""
            if title and hasattr(self, 'ai_manager'):
                try:
                    category = await self.ai_manager.get_category_openRouter(title)
                except Exception as e:
                    logger.error(f"Erro ao obter categoria com IA: {e}")
                    if self.log_callback:
                        self.log_callback(f"Erro ao obter categoria com IA: {e}")
                    category = "Outros"  # Categoria padrão

            return {
                "title": title,
                "price": price,
                "old_price": old_price,
                "discount": discount,
                "link": link,
                "image_url": image_url,
                "store": "Amazon",
                "category": category,
                "is_deal": is_deal
            }
        except Exception as e:
            if self.log_callback: # Envia error para o callback
                self.log_callback(f"Erro ao extrair dados do produto: {e}")
            logger.error(f"Erro ao extrair dados do produto: {e}")
            return None

    async def scrape_page(self, url):
        """Extrai produtos de uma página"""
        self.set_status(f"Processando página: {url}")
        html_content = await self._get_html_with_browser_simulation(url)

        if not html_content:
            error_msg = f"Não foi possível obter conteúdo da página: {url}"
            if self.log_callback:
                self.log_callback(error_msg)
            logger.error(error_msg)
            return [], None

        soup = BeautifulSoup(html_content, 'html.parser')

        # Encontra todos os produtos na página
        product_elements = soup.select(self.selectors['product_container'])
        info_msg = f"Encontrados {len(product_elements)} produtos na página"
        if self.log_callback:
            self.log_callback(info_msg)
        logger.info(info_msg)

        # Extrai dados de cada produto
        products = []
        for product_element in product_elements:
            product_data = await self.extract_product_data(product_element, self.base_url)
            if product_data:
                products.append(product_data)

        # Verifica se há próxima página
        next_page_element = soup.select_one(self.selectors['next_page'])
        next_page_url = None
        if next_page_element and next_page_element.get('href'):
            next_page_url = urljoin(self.base_url, next_page_element.get('href'))

        return products, next_page_url

    async def run(self, max_pages=3, only_discount=False):
        """
        Executa o scraper da Amazon e retorna os produtos encontrados
        """
        if self._is_running:
            logger.warning("O scraper já está em execução")
            if self.log_callback:
                self.log_callback("O scraper já está em execução")
            return []

        self._is_running = True
        self.set_status("Iniciando scraper da Amazon (apenas com desconto: {})".format(only_discount))

        products = []
        try:
            # Informações detalhadas sobre a configuração
            logger.info(f"Configuração do scraper: max_pages={max_pages}, only_discount={only_discount}")
            if self.log_callback:
                self.log_callback(f"Configuração do scraper: max_pages={max_pages}, apenas com desconto={only_discount}")

            logger.info(f"User-Agent sendo utilizado: {self.header_manager.get_random_header().get('User-Agent', 'Não definido')}")
            if self.log_callback:
                self.log_callback(f"User-Agent sendo utilizado: {self.header_manager.get_random_header().get('User-Agent', 'Não definido')}")

            all_products = []
            # Usar o deals_url como base para a paginação baseada em índice
            # current_url = self.deals_url # Removido

            # Assumindo que a URL da categoria já vem com o formato correto para paginação por índice
            # Ex: https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-pet-products&promotionsSearchLastSeenAsin=B07C66PZWS&promotionsSearchStartIndex={i}&promotionsSearchPageSize=60

            page_size = 60 # Tamanho da página para Amazon (atualizado para 60)

            # Precisa receber a URL template da categoria para usar a paginação por índice
            # Como o run do AmazonScraper não recebe a categoria, vou assumir que a URL base
            # já é a URL da primeira página da categoria com o {i} para o índice.
            # Isso pode precisar ser ajustado dependendo de como as categorias são passadas para o AmazonScraper.

            # Para fins de demonstração da paginação por índice, vou usar uma URL de exemplo
            # e iterar com base no page_size. Em um cenário real, a URL template viria das categorias.

            # Exemplo de URL template com {i} para o índice
            # url_template = "https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-pet-products&promotionsSearchStartIndex={i}&promotionsSearchPageSize=60"

            # Como não tenho a URL template da categoria aqui, vou simular a iteração
            # com base em um índice inicial e page_size.

            # TODO: Integrar com o sistema de categorias para obter a URL template correta
            # Por enquanto, vou usar uma URL de exemplo para testar a paginação por índice

            # Assumindo que a URL template da categoria é passada para este método ou para o __init__
            # Para este exemplo, vou usar uma URL fixa para demonstrar a paginação por índice

            # Exemplo de URL template (substituir pela URL real da categoria)
            url_template_example = "https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-pet-products&promotionsSearchStartIndex={i}&promotionsSearchPageSize=60"

            current_index = 0
            page_count = 0

            while page_count < max_pages and self._is_running:
                page_count += 1

                # Formatar URL com o índice de início
                current_url = url_template_example.format(i=current_index)

                self.set_status(f"Processando página {page_count} de {max_pages} (Index: {current_index})")

                products, _ = await self.scrape_page(current_url) # Ignorar next_page_url com paginação por índice

                if only_discount:
                    # Filtrar produtos que não têm preço antigo (sem desconto)
                    filtered_products = [p for p in products if p and p.get('old_price') is not None and p['old_price'] > 0] # Corrigido filtro
                    info_msg = f"Filtrados {len(products) - len(filtered_products)} produtos sem desconto na página {page_count}"
                    if self.log_callback:
                        self.log_callback(info_msg)
                    logger.info(info_msg)
                    all_products.extend(filtered_products)
                else:
                    all_products.extend(products)

                info_msg = f"Extraídos {len(products)} produtos da página {page_count}"
                if self.log_callback:
                    self.log_callback(info_msg)
                logger.info(info_msg)

                # Atualizar o índice para a próxima página
                current_index += page_size

                # Se não encontrou produtos na página, pode ser o fim da paginação
                if not products:
                    info_msg = f"Nenhum produto encontrado na página {page_count}, encerrando paginação."
                    if self.log_callback:
                        self.log_callback(info_msg)
                    logger.info(info_msg)
                    break # Sai do loop se não encontrou produtos

            self.set_status(f"Concluído. Extraídos {len(all_products)} produtos no total")
            return all_products

        except Exception as e:
            if self.log_callback: # Envia error para o callback
                self.log_callback(f"Erro durante a execução do scraper: {e}")
            logger.error(f"Erro durante a execução do scraper: {e}")
            self.set_status(f"Erro: {str(e)}")
            return []
        finally:
            self._is_running = False

    def save_products(self, products, filename="amazon_products.json"):
        """Salva os produtos extraídos em um arquivo JSON"""
        # Este método não será mais usado para salvar em CSV por categoria
        # A lógica de salvamento em CSV será movida para AmazonIntegration ou ExecutionFrame
        if self.log_callback: # Envia info para o callback
            self.log_callback(f"Salvando {len(products)} produtos em JSON (método save_products do AmazonScraper)")
        logger.info(f"Salvando {len(products)} produtos em JSON (método save_products do AmazonScraper)")
        try:
            output_dir = os.path.join(project_root, "output")
            os.makedirs(output_dir, exist_ok=True)

            output_path = os.path.join(output_dir, filename)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(products, f, ensure_ascii=False, indent=4)

            if self.log_callback: # Envia info para o callback
                self.log_callback(f"Produtos salvos em {output_path}")
            logger.info(f"Produtos salvos em {output_path}")
            return output_path
        except Exception as e:
            if self.log_callback: # Envia error para o callback
                self.log_callback(f"Erro ao salvar produtos: {e}")
            logger.error(f"Erro ao salvar produtos: {e}")
            return None

# Função para teste direto do módulo
if __name__ == "__main__":
    # Garantir que o diretório de logs exista
    logs_dir = os.path.join(project_root, "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Configuração de logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout), # Enviar logs para o console
            logging.FileHandler(os.path.join(project_root, "logs", "amazon_scraper.log"))
        ]
    )

    # Executa o scraper
    async def test_scraper():
        # Exemplo de log_callback para teste
        def print_log(message):
            print(f"[CALLBACK] {message}")

        scraper = AmazonScraper(log_callback=print_log) # Passa o callback
        products = await scraper.run(max_pages=2, only_discount=True) # Testa com filtro
        # scraper.save_products(products) # Salvamento em JSON removido do teste direto
        print(f"Teste concluído. Total de produtos extraídos: {len(products)}")

    # Executa o teste
    asyncio.run(test_scraper())
