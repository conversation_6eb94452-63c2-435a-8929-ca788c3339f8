"""
Adaptador para o scraper da Amazon usando Scrapy e métodos alternativos
"""

import logging
import random
import time
import os
import sys
import json
from typing import Optional
import asyncio
try:
    import aiohttp
except ImportError:
    logging.error("Módulo aiohttp não encontrado. Execute 'pip install aiohttp' para instalar.")
    aiohttp = None
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# Removido dependências do Scrapy - usando apenas requests/aiohttp e BeautifulSoup
from src.scrapers.base_scraper import BaseScraper
from utils.header_manager import HeaderManager

# Configurar logger
logger = logging.getLogger(__name__)

# Definir project_root
script_dir = os.path.dirname(os.path.abspath(__file__))
# Subir dois níveis: src/scrapers -> src -> raiz do projeto
project_root = os.path.dirname(os.path.dirname(script_dir))

# Adicionar project_root ao sys.path se ainda não estiver
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    logger.info(f"Adicionado {project_root} ao sys.path")

class AmazonScraper(BaseScraper):
    """
    Scraper para a Amazon com múltiplas estratégias de raspagem
    """

    def __init__(self, log_callback=None):
        super().__init__()
        self.store_id = "amazon"
        self.name = "Amazon"
        self.base_url = "https://www.amazon.com.br"
        self.deals_url = "https://www.amazon.com.br/deals"
        self.scrapy_available = True
        self.header_manager = HeaderManager()
        self._is_running = False
        self.current_status = "Não iniciado"
        self.log_callback = log_callback

        # Definir os seletores CSS para a Amazon (atualizados para funcionar)
        self.selectors = {
            'product_container': 'div[data-asin]',  # Seletor que funciona no Selenium
            'product_title': 'h3 a span, h2 a span, span.a-size-base-plus, span.a-size-medium, h3 span, h2 span, a span',
            'product_price': 'span.a-price-whole, span.a-price .a-offscreen, span.a-price-range .a-offscreen',
            'product_old_price': 'span.a-price.a-text-price .a-offscreen, span.a-text-strike .a-offscreen, span.a-price-was .a-offscreen',
            'product_link': 'h3 a, h2 a, a.a-link-normal',
            'product_image': 'img.s-image, img[data-src], img[src]',
            'deal_badge': 'span.a-badge-text',
            'next_page': 'a.s-pagination-next'
        }

        # Configurações de delay para evitar bloqueio
        self.delay_min = 2.0
        self.delay_max = 5.0

        logger.info("AmazonScraper inicializado com múltiplas estratégias")

    def set_status(self, status):
        """Atualiza o status atual do scraper"""
        self.current_status = status
        if self.log_callback: # Envia status para o callback
            self.log_callback(f"Status do AmazonScraper: {status}")
        logger.info(f"Status do AmazonScraper: {status}")

    def get_status(self):
        """Retorna o status atual do scraper"""
        return self.current_status

    def _add_random_delay(self):
        """Adiciona um delay aleatório entre requisições para evitar detecção"""
        delay = random.uniform(self.delay_min, self.delay_max)
        if self.log_callback: # Envia debug para o callback
            self.log_callback(f"Aguardando {delay:.2f} segundos antes da próxima requisição")
        logger.debug(f"Aguardando {delay:.2f} segundos antes da próxima requisição")

    def _clean_price(self, price_text):
        """Limpa e converte texto de preço para float"""
        if not price_text:
            return 0.0

        try:
            # Remover caracteres não numéricos exceto vírgula e ponto
            import re
            price_clean = re.sub(r'[^\d,.]', '', price_text)

            # Tratar formato brasileiro (vírgula como decimal)
            if ',' in price_clean and '.' in price_clean:
                # Formato: 1.234,56
                price_clean = price_clean.replace('.', '').replace(',', '.')
            elif ',' in price_clean:
                # Formato: 1234,56
                price_clean = price_clean.replace(',', '.')

            return float(price_clean) if price_clean else 0.0

        except Exception:
            return 0.0

    def _is_blocked_content(self, html_content):
        """Verifica se o conteúdo indica bloqueio"""
        if not html_content:
            return True

        content_lower = html_content.lower()
        blocked_keywords = ['robot', 'captcha', 'blocked', 'access denied', 'sorry', 'temporarily unavailable']

        return any(keyword in content_lower for keyword in blocked_keywords)

    async def _get_html_with_browser_simulation(self, url: str) -> Optional[str]:
        """
        Obtém o HTML usando simulação de navegador - agora usa requests como fallback
        """
        try:
            # Verificar se aiohttp está disponível
            if aiohttp is None:
                error_msg = "aiohttp não está instalado. Usando método requests."
                logger.warning(error_msg)
                if self.log_callback:
                    self.log_callback(error_msg)
                return self._get_html_with_requests(url)

            # Usar o HeaderManager para obter headers aleatórios
            headers = self.header_manager.get_random_header() if hasattr(self, 'header_manager') else {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

            # Adicionar delay aleatório para evitar detecção
            await asyncio.sleep(random.uniform(3.0, 7.0))

            # Configurar timeout mais longo
            timeout = aiohttp.ClientTimeout(total=30)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url, headers=headers, ssl=False) as response:
                    if response.status == 200:
                        return await response.text()
                    else:
                        error_msg = f"Erro ao acessar a URL {url}: {response.status} {response.reason}"
                        logger.error(error_msg)
                        if self.log_callback:
                            self.log_callback(error_msg)
                        return None
        except Exception as e:
            error_msg = f"Erro ao obter HTML com simulação de navegador: {e}"
            logger.error(error_msg)
            if self.log_callback:
                self.log_callback(error_msg)
            # Se falhar com aiohttp, tentar com requests
            return self._get_html_with_requests(url)

    def _get_html_with_requests(self, url: str) -> Optional[str]:
        """
        Método alternativo usando requests com múltiplas estratégias anti-bloqueio
        """
        try:
            import requests
            from requests.adapters import HTTPAdapter
            from urllib3.util.retry import Retry

            # Configurar sessão com retry automático
            session = requests.Session()
            retry_strategy = Retry(
                total=3,
                backoff_factor=2,
                status_forcelist=[429, 500, 502, 503, 504],
            )
            adapter = HTTPAdapter(max_retries=retry_strategy)
            session.mount("http://", adapter)
            session.mount("https://", adapter)

            # Headers mais realistas para evitar detecção
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0',
                'DNT': '1'
            }

            # Usar headers do HeaderManager se disponível
            if hasattr(self, 'header_manager') and self.header_manager:
                custom_headers = self.header_manager.get_random_header()
                if custom_headers:
                    headers.update(custom_headers)

            # Delay aleatório mais longo para evitar detecção
            delay = random.uniform(5.0, 10.0)
            if self.log_callback:
                self.log_callback(f"Aguardando {delay:.1f}s antes da requisição...")
            time.sleep(delay)

            # Fazer a requisição com timeout maior
            response = session.get(url, headers=headers, timeout=45, allow_redirects=True)

            if response.status_code == 200:
                # Verificar se há bloqueio por conteúdo
                content_lower = response.text.lower()
                if any(keyword in content_lower for keyword in ['robot', 'captcha', 'blocked', 'access denied']):
                    error_msg = "Amazon detectou bot - tentando com estratégia alternativa"
                    logger.warning(error_msg)
                    if self.log_callback:
                        self.log_callback(error_msg)
                    return self._get_html_with_alternative_strategy(url)

                if self.log_callback:
                    self.log_callback(f"✅ Requisição bem-sucedida - {len(response.text)} caracteres obtidos")
                return response.text

            elif response.status_code == 503:
                error_msg = f"Amazon retornou 503 (Service Unavailable) - tentando estratégia alternativa"
                logger.warning(error_msg)
                if self.log_callback:
                    self.log_callback(error_msg)
                return self._get_html_with_alternative_strategy(url)

            else:
                error_msg = f"Erro ao acessar a URL {url}: {response.status_code}"
                logger.error(error_msg)
                if self.log_callback:
                    self.log_callback(error_msg)
                return self._get_html_with_alternative_strategy(url)

        except Exception as e:
            error_msg = f"Erro ao obter HTML com requests: {e}"
            logger.error(error_msg)
            if self.log_callback:
                self.log_callback(error_msg)
            return self._get_html_with_alternative_strategy(url)

    def _get_html_with_alternative_strategy(self, url: str) -> Optional[str]:
        """
        Estratégia alternativa usando múltiplas URLs e headers diferentes
        """
        try:
            import requests

            # Lista de URLs alternativas para tentar
            urls_to_try = [
                "https://www.amazon.com.br/deals",  # URL simples
                "https://www.amazon.com.br/gp/bestsellers",  # Bestsellers
                "https://www.amazon.com.br/s?k=ofertas",  # Busca por ofertas
                "https://www.amazon.com.br/",  # Homepage
            ]

            # Headers mais básicos
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/119.0',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            for attempt, test_url in enumerate(urls_to_try):
                if self.log_callback:
                    self.log_callback(f"Estratégia alternativa - Tentativa {attempt + 1}: {test_url}")

                try:
                    # Delay maior entre tentativas
                    time.sleep(random.uniform(8.0, 15.0))

                    response = requests.get(test_url, headers=headers, timeout=30)

                    if response.status_code == 200:
                        if self.log_callback:
                            self.log_callback(f"✅ Estratégia alternativa funcionou na tentativa {attempt + 1} - {len(response.text)} caracteres")

                        # Verificar se não é página de bloqueio
                        if self._is_blocked_content(response.text):
                            if self.log_callback:
                                self.log_callback("❌ Conteúdo indica bloqueio, tentando próxima URL")
                            continue

                        return response.text

                    elif response.status_code == 503:
                        if self.log_callback:
                            self.log_callback(f"❌ 503 na tentativa {attempt + 1}, aguardando...")
                        time.sleep(10)  # Aguardar antes da próxima tentativa
                        continue

                    else:
                        if self.log_callback:
                            self.log_callback(f"❌ Status {response.status_code} na tentativa {attempt + 1}")
                        continue

                except Exception as e:
                    if self.log_callback:
                        self.log_callback(f"❌ Erro na tentativa {attempt + 1}: {e}")
                    continue

            # Se chegou aqui, todas as tentativas falharam
            if self.log_callback:
                self.log_callback("❌ Todas as estratégias alternativas falharam")
            return None

        except Exception as e:
            error_msg = f"Erro na estratégia alternativa: {e}"
            logger.error(error_msg)
            if self.log_callback:
                self.log_callback(error_msg)
            return None

    async def extract_product_data(self, product_element, base_url):
        """Extrai dados de um elemento de produto"""
        try:
            # Extrai título usando múltiplos seletores
            title = None
            title_selectors = self.selectors['product_title'].split(', ')
            for selector in title_selectors:
                title_elems = product_element.select(selector.strip())
                for title_elem in title_elems:
                    text = title_elem.get_text(strip=True)
                    if text and len(text) > 10:  # Título deve ter pelo menos 10 caracteres
                        title = text
                        break
                if title:
                    break

            if not title:
                title = "Sem título"

            # Extrai preço atual usando múltiplos seletores
            price = 0.0
            price_selectors = self.selectors['product_price'].split(', ')
            for selector in price_selectors:
                price_element = product_element.select_one(selector.strip())
                if price_element:
                    price_text = price_element.get_text(strip=True)
                    price = self._clean_price(price_text)
                    if price > 0:
                        break

            # Extrai preço antigo usando múltiplos seletores
            old_price = 0.0
            old_price_selectors = self.selectors['product_old_price'].split(', ')
            for selector in old_price_selectors:
                old_price_element = product_element.select_one(selector.strip())
                if old_price_element:
                    old_price_text = old_price_element.get_text(strip=True)
                    old_price = self._clean_price(old_price_text)
                    if old_price > 0:
                        break

            # Extrai link usando múltiplos seletores
            link = ""
            link_selectors = self.selectors['product_link'].split(', ')
            for selector in link_selectors:
                link_element = product_element.select_one(selector.strip())
                if link_element:
                    href = link_element.get('href')
                    if href:
                        if href.startswith('/'):
                            link = 'https://www.amazon.com.br' + href
                        else:
                            link = href
                        break

            # Extrai imagem usando múltiplos seletores
            image_url = ""
            image_selectors = self.selectors['product_image'].split(', ')
            for selector in image_selectors:
                image_element = product_element.select_one(selector.strip())
                if image_element:
                    image_url = image_element.get('data-src') or image_element.get('src')
                    if image_url and image_url.startswith('//'):
                        image_url = 'https:' + image_url
                    if image_url:
                        break

            # Verifica se é uma oferta
            deal_badge = product_element.select_one(self.selectors['deal_badge'])
            is_deal = bool(deal_badge)

            # Calcula desconto
            discount = 0
            if old_price > 0 and price > 0:
                discount = round(100 - (price * 100 / old_price))

            # Obtém categoria usando IA
            category = ""
            if title and hasattr(self, 'ai_manager'):
                try:
                    category = await self.ai_manager.get_category_openRouter(title)
                except Exception as e:
                    logger.error(f"Erro ao obter categoria com IA: {e}")
                    if self.log_callback:
                        self.log_callback(f"Erro ao obter categoria com IA: {e}")
                    category = "Outros"  # Categoria padrão

            return {
                "title": title,
                "price": price,
                "old_price": old_price,
                "discount": discount,
                "link": link,
                "image_url": image_url,
                "store": "Amazon",
                "category": category,
                "is_deal": is_deal
            }
        except Exception as e:
            if self.log_callback: # Envia error para o callback
                self.log_callback(f"Erro ao extrair dados do produto: {e}")
            logger.error(f"Erro ao extrair dados do produto: {e}")
            return None

    async def scrape_page(self, url):
        """Extrai produtos de uma página"""
        self.set_status(f"Processando página: {url}")

        # Tentar primeiro com aiohttp, depois com requests
        html_content = await self._get_html_with_browser_simulation(url)

        # Se falhou com aiohttp, tentar diretamente com requests
        if not html_content:
            if self.log_callback:
                self.log_callback("Tentativa com aiohttp falhou, usando requests...")
            html_content = self._get_html_with_requests(url)

        if not html_content:
            error_msg = f"Não foi possível obter conteúdo da página: {url}"
            if self.log_callback:
                self.log_callback(error_msg)
            logger.error(error_msg)
            return [], None

        soup = BeautifulSoup(html_content, 'html.parser')

        # Encontra todos os produtos na página
        product_elements = soup.select(self.selectors['product_container'])
        info_msg = f"Encontrados {len(product_elements)} produtos na página"
        if self.log_callback:
            self.log_callback(info_msg)
        logger.info(info_msg)

        # Debug: verificar se não encontrou produtos
        if not product_elements:
            debug_msg = f"Nenhum produto encontrado com seletor: {self.selectors['product_container']}"
            if self.log_callback:
                self.log_callback(debug_msg)
            logger.warning(debug_msg)

            # Tentar seletores alternativos
            alternative_selectors = [
                'div[data-component-type="s-search-result"]',
                'div.s-result-item',
                'div.a-section.a-spacing-base',
                'div[data-cel-widget]',
                'div.s-card-container'
            ]

            for alt_selector in alternative_selectors:
                alt_products = soup.select(alt_selector)
                debug_msg = f"Seletor alternativo {alt_selector}: {len(alt_products)} produtos"
                if self.log_callback:
                    self.log_callback(debug_msg)
                logger.info(debug_msg)

                if alt_products:
                    product_elements = alt_products
                    info_msg = f"Usando seletor alternativo: {alt_selector} - {len(product_elements)} produtos"
                    if self.log_callback:
                        self.log_callback(info_msg)
                    logger.info(info_msg)
                    break

        # Extrai dados de cada produto
        products = []
        for product_element in product_elements:
            product_data = await self.extract_product_data(product_element, self.base_url)
            if product_data:
                products.append(product_data)

        # Verifica se há próxima página
        next_page_element = soup.select_one(self.selectors['next_page'])
        next_page_url = None
        if next_page_element and next_page_element.get('href'):
            next_page_url = urljoin(self.base_url, next_page_element.get('href'))

        return products, next_page_url

    async def run(self, max_pages=3, only_discount=False):
        """
        Executa o scraper da Amazon e retorna os produtos encontrados
        """
        if self._is_running:
            logger.warning("O scraper já está em execução")
            if self.log_callback:
                self.log_callback("O scraper já está em execução")
            return []

        self._is_running = True
        self.set_status("Iniciando scraper da Amazon (apenas com desconto: {})".format(only_discount))

        products = []
        try:
            # Informações detalhadas sobre a configuração
            logger.info(f"Configuração do scraper: max_pages={max_pages}, only_discount={only_discount}")
            if self.log_callback:
                self.log_callback(f"Configuração do scraper: max_pages={max_pages}, apenas com desconto={only_discount}")

            logger.info(f"User-Agent sendo utilizado: {self.header_manager.get_random_header().get('User-Agent', 'Não definido')}")
            if self.log_callback:
                self.log_callback(f"User-Agent sendo utilizado: {self.header_manager.get_random_header().get('User-Agent', 'Não definido')}")

            all_products = []
            # Usar o deals_url como base para a paginação baseada em índice
            # current_url = self.deals_url # Removido

            # Assumindo que a URL da categoria já vem com o formato correto para paginação por índice
            # Ex: https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-pet-products&promotionsSearchLastSeenAsin=B07C66PZWS&promotionsSearchStartIndex={i}&promotionsSearchPageSize=60

            page_size = 60 # Tamanho da página para Amazon (atualizado para 60)

            # Precisa receber a URL template da categoria para usar a paginação por índice
            # Como o run do AmazonScraper não recebe a categoria, vou assumir que a URL base
            # já é a URL da primeira página da categoria com o {i} para o índice.
            # Isso pode precisar ser ajustado dependendo de como as categorias são passadas para o AmazonScraper.

            # Para fins de demonstração da paginação por índice, vou usar uma URL de exemplo
            # e iterar com base no page_size. Em um cenário real, a URL template viria das categorias.

            # Exemplo de URL template com {i} para o índice
            # url_template = "https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-pet-products&promotionsSearchStartIndex={i}&promotionsSearchPageSize=60"

            # Como não tenho a URL template da categoria aqui, vou simular a iteração
            # com base em um índice inicial e page_size.

            # TODO: Integrar com o sistema de categorias para obter a URL template correta
            # Por enquanto, vou usar uma URL de exemplo para testar a paginação por índice

            # Assumindo que a URL template da categoria é passada para este método ou para o __init__
            # Para este exemplo, vou usar uma URL fixa para demonstrar a paginação por índice

            # Usar URL simples que funciona (baseado nos testes)
            url_template_example = "https://www.amazon.com.br/deals"

            current_index = 0
            page_count = 0

            while page_count < max_pages and self._is_running:
                page_count += 1

                # Usar URL simples (sem paginação por índice)
                current_url = url_template_example

                self.set_status(f"Processando página {page_count} de {max_pages}")

                products, next_page_url_from_scrape = await self.scrape_page(current_url) # next_page_url_from_scrape será None com paginação por índice

                if only_discount:
                    # Filtrar produtos que não têm preço antigo (sem desconto)
                    filtered_products = [p for p in products if p and p.get('old_price') is not None and p['old_price'] > 0] # Corrigido filtro
                    info_msg = f"Filtrados {len(products) - len(filtered_products)} produtos sem desconto na página {page_count}"
                    if self.log_callback:
                        self.log_callback(info_msg)
                    logger.info(info_msg)
                    all_products.extend(filtered_products)
                else:
                    all_products.extend(products)

                info_msg = f"Extraídos {len(products)} produtos da página {page_count}"
                if self.log_callback:
                    self.log_callback(info_msg)
                logger.info(info_msg)

                # Atualizar o índice para a próxima página
                current_index += page_size

                # Se não encontrou produtos na página, pode ser o fim da paginação
                if not products:
                    info_msg = f"Nenhum produto encontrado na página {page_count}, encerrando paginação."
                    if self.log_callback:
                        self.log_callback(info_msg)
                    logger.info(info_msg)
                    break # Sai do loop se não encontrou produtos

            self.set_status(f"Concluído. Extraídos {len(all_products)} produtos no total")
            return all_products

        except Exception as e:
            if self.log_callback: # Envia error para o callback
                self.log_callback(f"Erro durante a execução do scraper: {e}")
            logger.error(f"Erro durante a execução do scraper: {e}")
            self.set_status(f"Erro: {str(e)}")
            return []
        finally:
            self._is_running = False

    def save_products(self, products, filename="amazon_products.json"):
        """Salva os produtos extraídos em um arquivo JSON"""
        # Este método não será mais usado para salvar em CSV por categoria
        # A lógica de salvamento em CSV será movida para AmazonIntegration ou ExecutionFrame
        if self.log_callback: # Envia info para o callback
            self.log_callback(f"Salvando {len(products)} produtos em JSON (método save_products do AmazonScraper)")
        logger.info(f"Salvando {len(products)} produtos em JSON (método save_products do AmazonScraper)")
        try:
            output_dir = os.path.join(project_root, "output")
            os.makedirs(output_dir, exist_ok=True)

            output_path = os.path.join(output_dir, filename)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(products, f, ensure_ascii=False, indent=4)

            if self.log_callback: # Envia info para o callback
                self.log_callback(f"Produtos salvos em {output_path}")
            logger.info(f"Produtos salvos em {output_path}")
            return output_path
        except Exception as e:
            if self.log_callback: # Envia error para o callback
                self.log_callback(f"Erro ao salvar produtos: {e}")
            logger.error(f"Erro ao salvar produtos: {e}")
            return None

# Função para teste direto do módulo
if __name__ == "__main__":
    # Garantir que o diretório de logs exista
    logs_dir = os.path.join(project_root, "logs")
    os.makedirs(logs_dir, exist_ok=True)

    # Configuração de logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout), # Enviar logs para o console
            logging.FileHandler(os.path.join(project_root, "logs", "amazon_scraper.log"))
        ]
    )

    # Executa o scraper
    async def test_scraper():
        # Exemplo de log_callback para teste
        def print_log(message):
            print(f"[CALLBACK] {message}")

        scraper = AmazonScraper(log_callback=print_log) # Passa o callback
        products = await scraper.run(max_pages=2, only_discount=True) # Testa com filtro
        # scraper.save_products(products) # Salvamento em JSON removido do teste direto
        print(f"Teste concluído. Total de produtos extraídos: {len(products)}")

    # Executa o teste
    asyncio.run(test_scraper())
