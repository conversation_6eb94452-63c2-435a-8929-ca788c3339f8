import requests
from bs4 import BeautifulSoup

def test_scraper_fields(url_template, log_callback=print, store_name="MercadoLivre"):
    """
    Testa se os campos do scraper estão funcionando corretamente

    Args:
        url_template: Template da URL a ser testada
        log_callback: Função para logging (default: print)
        store_name: <PERSON><PERSON> da loja (default: MercadoLivre)

    Retorna: (sucesso, mensagem)
    """
    # Formatar URL de acordo com a loja
    if store_name == "MercadoLivre":
        url = url_template.replace("{i}", "1")
    elif store_name == "Magalu":
        if "?" in url_template:
            url = f"{url_template}&page=1"
        else:
            url = f"{url_template}?page=1"
    elif store_name == "Amazon":
        url = url_template.replace("{i}", "0")
    else:
        url = url_template.replace("{i}", "1")

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
        }

        log_callback(f"Testando URL: {url}")

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # Salvar HTML para depuração
        debug_file = "debug_page.html"
        with open(debug_file, "w", encoding="utf-8") as f:
            f.write(response.text)

        soup = BeautifulSoup(response.text, 'html.parser')

        # Seletores específicos para cada loja
        if store_name == "MercadoLivre":
            selectors = [
                'div.andes-card.poly-card--grid-card',
                'div.andes-card',
                'div.ui-search-result',
                'li.ui-search-layout__item',
            ]
        elif store_name == "Magalu":
            selectors = [
                'div.product-card',
                'div.product-item',
                'li.product-list-item',
                'div.productCard',
                'div.sc-dPiLbb',
                'div.sc-hKwDye',
                'div.sc-jrQzAO',
                'div.sc-kDTinF',
                'div.sc-iqseJM',
                'a[data-testid="product-card-container"]',
                'div[data-testid="product-card"]',
                'li.neemu-product-list',
                'div.nm-product-item',
                'div.product-li',
                'div.showcase-product',
                'div.product-card',
                'div.product-grid-item',
                'div.product-box',
                'div.product-container',
                'div.product-wrapper'
            ]
        elif store_name == "Amazon":
            selectors = [
                'div[data-testid][data-asin]',
                'div.ProductCard-module__card_uyr_Jh7WpSkPx4iEpn4w',
                'div.GridItem-module__container_PW2gdkwTj1GQzdwJjejN',
                'div.a-section.a-spacing-base',
                'div[data-asin]',
                'div.s-result-item',
                'div.a-section.a-spacing-small',
                'div.product-card',
                'div.deal-card'
            ]
        else:
            selectors = [
                'div.andes-card.poly-card--grid-card',
                'div.andes-card',
                'div.ui-search-result',
                'li.ui-search-layout__item',
                'div.product-card',
                'div.product-item',
                'li.product-list-item',
            ]

        # Tentar encontrar produtos com os seletores
        for selector in selectors:
            products = soup.select(selector)
            if products:
                log_callback(f"✓ {len(products)} produtos encontrados com seletor: {selector}")

                # Testar apenas o primeiro produto sem mostrar detalhes
                product = products[0]
                fields_found = _test_product_fields(product, store_name)

                if not fields_found:
                    return False, "Campos necessários não encontrados no produto"

                return True, f"Teste bem sucedido: {len(products)} produtos encontrados com seletor: {selector}"

        # Se não encontrou com os seletores específicos, tentar seletores genéricos
        log_callback("Tentando seletores genéricos...")
        generic_selectors = [
            'div', 'li', 'a', 'article', 'section'
        ]

        for selector in generic_selectors:
            elements = soup.select(selector)
            for element in elements:
                # Verificar se este elemento parece um produto
                if _looks_like_product(element):
                    log_callback(f"Possível produto encontrado com seletor genérico: {selector}")
                    fields_found = _test_product_fields(element, store_name)
                    if fields_found:
                        return True, f"Teste bem sucedido com seletor genérico: {selector}"

        return False, "Não foi possível encontrar produtos na página"

    except requests.RequestException as e:
        return False, f"Erro na requisição: {str(e)}"
    except Exception as e:
        return False, f"Erro inesperado: {str(e)}"

def _looks_like_product(element):
    """Verifica se um elemento parece ser um produto"""
    # Verificar se tem imagem
    has_image = bool(element.select_one('img'))

    # Verificar se tem link
    has_link = bool(element.select_one('a'))

    # Verificar se tem algum texto que parece preço (R$, $, etc)
    text = element.get_text().lower()
    has_price = 'r$' in text or '$' in text or 'reais' in text or 'por:' in text

    # Verificar se tem algum atributo que sugere que é um produto
    attrs = ' '.join(element.attrs.get('class', [])).lower()
    product_attrs = 'product' in attrs or 'item' in attrs or 'card' in attrs

    # Verificar tamanho mínimo (produtos geralmente não são muito pequenos)
    min_size = len(element.get_text()) > 20

    # Retorna True se parece ser um produto
    return (has_image and has_link) or (has_price and min_size) or product_attrs

def _test_product_fields(product, store_name="MercadoLivre"):
    """Testa os campos de um produto sem logging"""
    # Seletores específicos para cada loja
    if store_name == "MercadoLivre":
        # Seletores de título
        title_selectors = [
            'a.poly-component__title',
            'h2.ui-search-item__title',
            'span.promotion-item__title',
            'h2.ui-search-item__group__element',
        ]

        # Seletores de preço
        price_selectors = [
            '.andes-money-amount__fraction',
            '.price-tag-amount',
            '.promotion-item__price',
            '.ui-search-price__part'
        ]

        # Seletores de link
        link_selectors = [
            'a.poly-component__title',
            'a.ui-search-item__group__element',
            'a.promotion-item__link',
            'a[title]'
        ]

        # Seletores de imagem
        image_selectors = [
            'img.ui-search-result-image__element',
            'img.promotion-item__img',
            'img[data-src]',
            'img[src]'
        ]
    elif store_name == "Magalu":
        # Seletores de título
        title_selectors = [
            'h3.product-title',
            'h2.product-name',
            'div.product-card__title',
            'h2.sc-eCImPb',
            'h3.sc-gsDKAQ',
            'span.sc-hKwDye',
            'div.sc-jrQzAO',
            '.product-title',
            '.product-name',
            '.nm-product-name',
            'h3', 'h2', 'h4'
        ]

        # Seletores de preço
        price_selectors = [
            'span.price-current',
            'div.product-price__current',
            'span.product-card__price',
            'p.sc-gsDKAQ',
            'span.sc-hKwDye',
            'div.sc-jrQzAO',
            'p.sc-eCImPb',
            '.price',
            '.product-price',
            '.nm-price',
            '.price-value',
            '.price-current'
        ]

        # Seletores de link
        link_selectors = [
            'a.product-card__link',
            'a.product-item__link',
            'a.product-list-item__link',
            'a.sc-gsDKAQ',
            'a.sc-hKwDye',
            'a.sc-jrQzAO',
            'a[data-testid="product-card-container"]',
            'a'
        ]

        # Seletores de imagem
        image_selectors = [
            'img.product-card__image',
            'img.product-item__image',
            'img.product-list-item__image',
            'img.sc-gsDKAQ',
            'img.sc-hKwDye',
            'img.sc-jrQzAO',
            'img[data-testid="image"]',
            'img'
        ]
    elif store_name == "Amazon":
        # Seletores de título
        title_selectors = [
            'p[id^="title-"] span.a-truncate-cut',
            'p[id^="title-"] span.a-truncate-full',
            'span.a-truncate-cut',
            'span.a-truncate-full',
            'h3.a-size-base-plus',
            'h2.a-size-mini',
            'span.a-size-base-plus'
        ]

        # Seletores de preço
        price_selectors = [
            'div[data-testid="price-section"] span.a-price span.a-offscreen',
            'span.a-price span.a-offscreen',
            'span.a-price-whole',
            'span.a-price-fraction',
            '.a-price',
            '.price'
        ]

        # Seletores de link
        link_selectors = [
            'a[data-testid="product-card-link"]',
            'a.a-link-normal',
            'a[href*="/dp/"]',
            'a'
        ]

        # Seletores de imagem
        image_selectors = [
            'img.a-amazon-image',
            'img.ProductCardImage-module__image',
            'img[src*="images-amazon"]',
            'img'
        ]
    else:
        # Seletores genéricos
        title_selectors = [
            'h1', 'h2', 'h3', 'h4', 'span.title', 'div.title', '.product-title', '.product-name', 'a[title]'
        ]

        price_selectors = [
            '.price', '.product-price', '.price-value', '.price-current', '.price-tag', '.price-amount'
        ]

        link_selectors = [
            'a', 'a[href]', 'a.product-link', 'a.item-link'
        ]

        image_selectors = [
            'img', 'img[src]', 'img[data-src]', '.product-image', '.item-image'
        ]

    # Verificar campos
    title = _find_field(product, title_selectors)
    price = _find_field(product, price_selectors)
    link = _find_field(product, link_selectors, attr='href')
    image = _find_field(product, image_selectors, attr=['data-src', 'src'])

    # Debug - imprimir valores encontrados
    print(f"DEBUG - Título: {title}")
    print(f"DEBUG - Preço: {price}")
    print(f"DEBUG - Link: {link}")
    print(f"DEBUG - Imagem: {image}")

    # Se não encontrou o título, tenta buscar em qualquer elemento com texto
    if not title:
        for elem in product.find_all(['h1', 'h2', 'h3', 'h4', 'span', 'div', 'a']):
            text = elem.get_text().strip()
            if text and len(text) > 10 and len(text) < 200:
                title = text
                break

    # Se não encontrou o preço, tenta buscar em qualquer elemento com texto que contenha R$ ou $
    if not price:
        for elem in product.find_all(['span', 'div', 'p']):
            text = elem.get_text().strip()
            if ('R$' in text or '$' in text) and len(text) < 50:
                price = text
                break

    # Se não encontrou o link, tenta buscar qualquer link no produto
    if not link:
        # Para o Magalu, o próprio elemento pode ser o link
        if product.name == 'a' and product.get('href'):
            link = product.get('href')
        else:
            links = product.find_all('a')
            if links:
                link = links[0].get('href', '')

    # Se não encontrou a imagem, tenta buscar qualquer imagem no produto
    if not image:
        images = product.find_all('img')
        if images:
            image = images[0].get('src', '') or images[0].get('data-src', '')

    # Debug - imprimir valores após tentativas adicionais
    print(f"DEBUG FINAL - Título: {title}")
    print(f"DEBUG FINAL - Preço: {price}")
    print(f"DEBUG FINAL - Link: {link}")
    print(f"DEBUG FINAL - Imagem: {image}")

    # Verificar se todos os campos foram encontrados
    all_fields_found = all([title, price, link, image])
    print(f"DEBUG - Todos os campos encontrados: {all_fields_found}")

    # Para o Magalu, vamos retornar True mesmo se o link estiver faltando (temporariamente)
    if store_name == "Magalu" and title and price and image:
        return True

    return all_fields_found

def _find_field(product, selectors, attr=None):
    """Procura um campo usando múltiplos seletores"""
    for selector in selectors:
        elem = product.select_one(selector)
        if elem:
            if attr:
                if isinstance(attr, list):
                    for a in attr:
                        value = elem.get(a)
                        if value:
                            return value
                else:
                    return elem.get(attr)
            else:
                return elem.get_text().strip()
    return None
