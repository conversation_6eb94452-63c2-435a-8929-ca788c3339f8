Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB750, 0007FFFFA650) msys-2.0.dll+0x1FE8E
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210286019, 0007FFFFB608, 0007FFFFB750, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB750  000210068E24 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA30  00021006A225 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAD2A70000 ntdll.dll
7FFAD1860000 KERNEL32.DLL
7FFACFDB0000 KERNELBASE.dll
7FFACBDF0000 apphelp.dll
7FFAD1300000 USER32.dll
7FFAD0280000 win32u.dll
7FFAD14B0000 GDI32.dll
7FFAD0680000 gdi32full.dll
7FFAD01E0000 msvcp_win.dll
7FFAD0370000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFAD1930000 advapi32.dll
7FFAD1590000 msvcrt.dll
7FFAD0860000 sechost.dll
7FFACFD80000 bcrypt.dll
7FFAD0B30000 RPCRT4.dll
7FFACF590000 CRYPTBASE.DLL
7FFAD0600000 bcryptPrimitives.dll
7FFAD2020000 IMM32.DLL
