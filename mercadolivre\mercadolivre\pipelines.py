import csv


class CategoryCSVPipeline:
    """
    Pipeline para exportar itens para arquivos CSV separados por categoria
    """

    def __init__(self):
        self.file_handles = {}
        self.csv_writers = {}
        self.count_by_category = {}

    def process_item(self, item, spider):
        category = item.get("category", "unknown")

        # Criar um arquivo para esta categoria se ele ainda não existir
        if category not in self.file_handles:
            filename = f"{category.replace(' ', '_')}.csv"
            self.file_handles[category] = open(
                filename, "w", newline="", encoding="utf-8"
            )
            self.csv_writers[category] = csv.DictWriter(
                self.file_handles[category], fieldnames=item.keys()
            )
            self.csv_writers[category].writeheader()
            self.count_by_category[category] = 0
            spider.logger.info(
                f"Criado arquivo CSV para a categoria {category}: {filename}"
            )

        # Escrever o item no arquivo da categoria correspondente
        self.csv_writers[category].writerow(item)
        self.count_by_category[category] += 1

        return item

    def close_spider(self, spider):
        # Fechar todos os arquivos abertos
        for category, file_handle in self.file_handles.items():
            file_handle.close()
            spider.logger.info(
                f"Categoria {category}: {self.count_by_category[category]} produtos encontrados"
            )
