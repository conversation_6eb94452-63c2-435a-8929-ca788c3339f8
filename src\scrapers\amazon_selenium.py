import os
import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

# Adiciona o diretório raiz ao PYTHONPATH
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

logger = logging.getLogger(__name__)

class AmazonSeleniumScraper:
    def __init__(self, headless=True):
        self.driver = None
        self.headless = headless
        self.setup_driver()

    def setup_driver(self):
        """Configura o driver do Selenium"""
        try:
            chrome_options = Options()

            if self.headless:
                chrome_options.add_argument("--headless")

            # Configurações para evitar detecção
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # User agent realista
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Configurações de janela
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--start-maximized")

            # Usar ChromeDriverManager para baixar automaticamente o driver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Remover propriedades que indicam automação
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("Driver Selenium configurado com sucesso")

        except Exception as e:
            logger.error(f"Erro ao configurar driver Selenium: {e}")
            raise

    def scrape_amazon_page(self, url, max_products=60):
        """Scraping de uma página da Amazon"""
        try:
            logger.info(f"Acessando URL: {url}")

            # Navegar para a página
            self.driver.get(url)

            # Aguardar carregamento com delay maior para evitar detecção
            time.sleep(8)

            # Aguardar elementos carregarem com timeout maior
            try:
                WebDriverWait(self.driver, 20).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div[data-asin]"))
                )
            except TimeoutException:
                logger.warning("Timeout aguardando produtos carregarem")

            # Delay adicional após carregamento
            time.sleep(3)

            # Obter HTML da página
            html = self.driver.page_source
            soup = BeautifulSoup(html, 'html.parser')

            logger.info(f"Tamanho do HTML: {len(html)} caracteres")
            logger.info(f"Título da página: {self.driver.title}")

            # Verificar se há bloqueio
            if "robot" in html.lower() or "captcha" in html.lower():
                logger.warning("Possível bloqueio detectado na Amazon")

            # Seletores para produtos da Amazon
            product_selectors = [
                'div[data-asin]',
                'div[data-component-type="s-search-result"]',
                'div.s-result-item',
                'div.a-section.a-spacing-base',
                'div[data-cel-widget]',
                'div.s-card-container'
            ]

            products = []
            for selector in product_selectors:
                products = soup.select(selector)
                logger.info(f"Seletor {selector}: {len(products)} elementos")
                if products:
                    logger.info(f"Encontrados {len(products)} produtos com seletor: {selector}")
                    break

            if not products:
                logger.warning("Nenhum produto encontrado")
                # Salvar HTML para debug
                with open('debug_amazon.html', 'w', encoding='utf-8') as f:
                    f.write(html)
                logger.info("HTML salvo em debug_amazon.html para análise")
                return []

            # Processar produtos
            processed_products = []
            for idx, product in enumerate(products[:max_products], 1):
                try:
                    product_data = self.extract_product_data(product, url)
                    if product_data:  # Só adiciona se tiver desconto real
                        processed_products.append(product_data)
                        logger.info(f"Produto {idx} COM DESCONTO processado: {product_data.get('titulo', 'Sem título')[:50]}...")

                except Exception as e:
                    logger.error(f"Erro ao processar produto {idx}: {e}")
                    continue

            logger.info(f"Total de produtos COM DESCONTO processados: {len(processed_products)}")
            return processed_products

        except Exception as e:
            logger.error(f"Erro no scraping da Amazon: {e}")
            return []

    def extract_product_data(self, product, base_url):
        """Extrai dados de um produto da Amazon"""
        try:
            # Extrair título - evitar textos de desconto
            title_selectors = [
                'h3 a span',
                'h2 a span',
                'span[data-cy="title-recipe-title"]',
                'span.a-size-base-plus',
                'span.a-size-medium',
                'h3 span',
                'h2 span',
                'a span'
            ]

            title = None
            for selector in title_selectors:
                title_elems = product.select(selector)
                for title_elem in title_elems:
                    text = title_elem.get_text(strip=True)
                    # Filtrar textos que são claramente descontos ou ofertas
                    if (text and len(text) > 15 and
                        not text.lower().startswith(('off', '%', 'oferta')) and
                        'off' not in text.lower()[:10]):  # Evitar "11% offOferta"
                        title = text
                        break
                if title:
                    break

            # Se não encontrou título válido, tentar extrair de outros elementos
            if not title:
                # Tentar extrair do atributo alt da imagem
                img_elem = product.select_one('img')
                if img_elem and img_elem.get('alt'):
                    alt_text = img_elem.get('alt').strip()
                    if len(alt_text) > 15:
                        title = alt_text

                # Se ainda não encontrou, usar um título genérico
                if not title:
                    title = "Produto Amazon"
                    logger.warning("Título específico não encontrado, usando genérico")

            # Extrair preços - Amazon usa estrutura específica para ofertas
            price = 0.0
            old_price = 0.0

            # Primeiro, tentar extrair do badge de desconto se existir
            discount_badge = product.select_one('span.a-size-mini')
            discount_percentage = 0
            if discount_badge:
                badge_text = discount_badge.get_text(strip=True)
                if 'off' in badge_text.lower() and '%' in badge_text:
                    try:
                        discount_percentage = int(badge_text.replace('%', '').replace('off', '').strip())
                    except:
                        pass

            # Tentar extrair preços de diferentes estruturas da Amazon
            # 1. Estrutura de busca com desconto (nova estrutura)
            # Preço atual (primeiro span.a-price)
            price_container = product.select_one('span.a-price[data-a-size="xl"]')
            if price_container:
                current_price_elem = price_container.select_one('.a-offscreen')
                if current_price_elem:
                    price_text = current_price_elem.get_text(strip=True)
                    price = self.clean_price(price_text)

            # 2. Preço original (riscado) - APENAS se realmente riscado
            # Verificar se existe elemento com data-a-strike="true" (preço riscado)
            old_price_container = product.select_one('span.a-price.a-text-price[data-a-strike="true"]')
            if old_price_container:
                old_price_elem = old_price_container.select_one('.a-offscreen')
                if old_price_elem:
                    old_price_text = old_price_elem.get_text(strip=True)
                    old_price = self.clean_price(old_price_text)
                    logger.debug(f"Preço riscado encontrado: {old_price_text} -> R$ {old_price}")

            # 3. Fallback para estruturas antigas
            if price == 0.0:
                price_container = product.select_one('span.a-price')
                if price_container:
                    current_price_elem = price_container.select_one('.a-offscreen')
                    if current_price_elem:
                        price_text = current_price_elem.get_text(strip=True)
                        price = self.clean_price(price_text)

            # REMOVIDO: Fallback para preços antigos
            # Só aceitar preços antigos se realmente tiverem data-a-strike="true"

            # 3. Se não encontrou preços, tentar extrair de outros seletores
            if price == 0.0:
                # Tentar seletores alternativos para preço
                alternative_price_selectors = [
                    '.a-price-whole',
                    '.a-price-fraction',
                    'span[aria-label*="R$"]',
                    'span[data-a-color="price"]'
                ]

                for selector in alternative_price_selectors:
                    price_elem = product.select_one(selector)
                    if price_elem:
                        price_text = price_elem.get_text(strip=True)
                        price = self.clean_price(price_text)
                        if price > 0:
                            break

            # REMOVIDO: Fallback alternativo para preços antigos
            # Só aceitar preços antigos com seletor específico de desconto

            # Se não encontrou preços nos elementos HTML, tentar calcular baseado no desconto
            if price == 0.0 and old_price == 0.0 and discount_percentage > 0:
                # Tentar encontrar qualquer referência de preço no HTML do produto
                product_html = str(product)
                import re

                # Procurar por padrões de preço no HTML
                price_patterns = [
                    r'R\$\s*[\d.,]+',
                    r'"price":\s*[\d.,]+',
                    r'"fullPrice":\s*[\d.,]+',
                    r'price:\s*[\d.,]+',
                ]

                found_prices = []
                for pattern in price_patterns:
                    matches = re.findall(pattern, product_html)
                    for match in matches:
                        # Extrair apenas os números
                        price_value = self.clean_price(match)
                        if price_value > 0:
                            found_prices.append(price_value)

                # Se encontrou preços, usar a lógica de desconto
                if found_prices:
                    # Assumir que o maior preço é o preço original
                    old_price = max(found_prices)
                    # Calcular preço com desconto
                    price = old_price * (100 - discount_percentage) / 100
                    logger.info(f"Preços calculados via desconto: Original R$ {old_price}, Atual R$ {price} ({discount_percentage}% off)")

            # Debug: log dos preços encontrados
            logger.debug(f"Preços extraídos - Atual: R$ {price}, Antigo: R$ {old_price}")

            # Extrair link
            link_selectors = [
                'h3.a-size-base-plus a',
                'h2.a-size-mini a',
                'a.a-link-normal'
            ]

            link = None
            for selector in link_selectors:
                link_elem = product.select_one(selector)
                if link_elem:
                    href = link_elem.get('href')
                    if href:
                        if href.startswith('/'):
                            link = 'https://www.amazon.com.br' + href
                        else:
                            link = href
                        break

            # Extrair imagem
            image_selectors = [
                'img.s-image',
                'img[data-src]',
                'img[src]'
            ]

            image_url = None
            for selector in image_selectors:
                img_elem = product.select_one(selector)
                if img_elem:
                    image_url = img_elem.get('data-src') or img_elem.get('src')
                    if image_url and image_url.startswith('//'):
                        image_url = 'https:' + image_url
                    break

            # Calcular desconto
            discount = 0
            if old_price > 0 and price > 0:
                discount = round(100 - (price * 100 / old_price))

            # VALIDAÇÃO: Só retornar produto se tiver desconto real
            # Se old_price for 0, significa que não há desconto real detectado
            if old_price == 0:
                logger.info(f"❌ Produto SEM desconto real: {title[:50]}... (preço atual: R$ {price})")
                return None
            else:
                logger.info(f"✅ Produto COM desconto real: {title[:50]}... (de R$ {old_price} por R$ {price})")

            return {
                'plataforma': 'Amazon',
                'titulo': title,
                'preco_atual': price,
                'preco_antigo': old_price,
                'desconto': discount,
                'url_produto': link,
                'url_afiliado': link,
                'url_imagem': image_url,
                'categoria': 'Melhor Amigo',  # Categoria fixa por enquanto
                'ativo': True
            }

        except Exception as e:
            logger.error(f"Erro ao extrair dados do produto: {e}")
            return None

    def clean_price(self, price_text):
        """Limpa e converte texto de preço para float"""
        if not price_text:
            return 0.0

        try:
            # Remover caracteres não numéricos exceto vírgula e ponto
            import re
            price_clean = re.sub(r'[^\d,.]', '', price_text)

            # Tratar formato brasileiro (vírgula como decimal)
            if ',' in price_clean and '.' in price_clean:
                # Formato: 1.234,56
                price_clean = price_clean.replace('.', '').replace(',', '.')
            elif ',' in price_clean:
                # Formato: 1234,56
                price_clean = price_clean.replace(',', '.')

            return float(price_clean) if price_clean else 0.0

        except Exception:
            return 0.0

    def close(self):
        """Fecha o driver"""
        if self.driver:
            self.driver.quit()
            logger.info("Driver Selenium fechado")

def test_amazon_selenium():
    """Teste do scraper Selenium da Amazon"""
    scraper = None
    try:
        print("🔍 Testando Amazon com Selenium...")

        scraper = AmazonSeleniumScraper(headless=True)

        # URL de teste - categoria específica
        url = "https://www.amazon.com.br/s?k=Armazenamento+e+Organiza%C3%A7%C3%A3o+para+Casa&i=hi&rh=n%3A17113550011&page=1&c=ts&qid=1748482675&ts_id=17113550011&xpid=dPcFseTBsHPIR&ref=sr_pg_1"

        products = scraper.scrape_amazon_page(url, max_products=10)

        print(f"✅ Encontrados {len(products)} produtos")

        for i, product in enumerate(products[:3], 1):
            print(f"Produto {i}: {product['titulo'][:50]}... - R$ {product['preco_atual']}")

        return products

    except Exception as e:
        print(f"❌ Erro: {e}")
        return []
    finally:
        if scraper:
            scraper.close()

if __name__ == "__main__":
    test_amazon_selenium()
