import os
import sys
import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

# Adiciona o diretório raiz ao PYTHONPATH
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

logger = logging.getLogger(__name__)

class AmazonSeleniumScraper:
    def __init__(self, headless=True):
        self.driver = None
        self.headless = headless
        self.setup_driver()

    def setup_driver(self):
        """Configura o driver do Selenium"""
        try:
            chrome_options = Options()

            if self.headless:
                chrome_options.add_argument("--headless")

            # Configurações para evitar detecção
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # User agent realista
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Configurações de janela
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--start-maximized")

            # Usar ChromeDriverManager para baixar automaticamente o driver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Remover propriedades que indicam automação
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            logger.info("Driver Selenium configurado com sucesso")

        except Exception as e:
            logger.error(f"Erro ao configurar driver Selenium: {e}")
            raise

    def scrape_amazon_page(self, url, max_products=60):
        """Scraping de uma página da Amazon"""
        try:
            logger.info(f"Acessando URL: {url}")

            # Navegar para a página
            self.driver.get(url)

            # Aguardar carregamento
            time.sleep(3)

            # Aguardar elementos carregarem
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "div[data-asin]"))
                )
            except TimeoutException:
                logger.warning("Timeout aguardando produtos carregarem")

            # Obter HTML da página
            html = self.driver.page_source
            soup = BeautifulSoup(html, 'html.parser')

            logger.info(f"Tamanho do HTML: {len(html)} caracteres")
            logger.info(f"Título da página: {self.driver.title}")

            # Verificar se há bloqueio
            if "robot" in html.lower() or "captcha" in html.lower():
                logger.warning("Possível bloqueio detectado na Amazon")

            # Seletores para produtos da Amazon
            product_selectors = [
                'div[data-asin]',
                'div[data-component-type="s-search-result"]',
                'div.s-result-item',
                'div.a-section.a-spacing-base',
                'div[data-cel-widget]',
                'div.s-card-container'
            ]

            products = []
            for selector in product_selectors:
                products = soup.select(selector)
                logger.info(f"Seletor {selector}: {len(products)} elementos")
                if products:
                    logger.info(f"Encontrados {len(products)} produtos com seletor: {selector}")
                    break

            if not products:
                logger.warning("Nenhum produto encontrado")
                # Salvar HTML para debug
                with open('debug_amazon.html', 'w', encoding='utf-8') as f:
                    f.write(html)
                logger.info("HTML salvo em debug_amazon.html para análise")
                return []

            # Processar produtos
            processed_products = []
            for idx, product in enumerate(products[:max_products], 1):
                try:
                    product_data = self.extract_product_data(product, url)
                    if product_data:
                        processed_products.append(product_data)
                        logger.info(f"Produto {idx} processado: {product_data.get('titulo', 'Sem título')[:50]}...")

                except Exception as e:
                    logger.error(f"Erro ao processar produto {idx}: {e}")
                    continue

            logger.info(f"Total de produtos processados: {len(processed_products)}")
            return processed_products

        except Exception as e:
            logger.error(f"Erro no scraping da Amazon: {e}")
            return []

    def extract_product_data(self, product, base_url):
        """Extrai dados de um produto da Amazon"""
        try:
            # Extrair título
            title_selectors = [
                'h3 a span',
                'h2 a span',
                'span[data-cy="title-recipe-title"]',
                'span.a-size-base-plus',
                'span.a-size-medium',
                'h3 span',
                'h2 span',
                'a span'
            ]

            title = None
            for selector in title_selectors:
                title_elems = product.select(selector)
                for title_elem in title_elems:
                    text = title_elem.get_text(strip=True)
                    if text and len(text) > 10:  # Título deve ter pelo menos 10 caracteres
                        title = text
                        break
                if title:
                    break

            if not title:
                logger.warning("Título não encontrado para produto")
                return None

            # Extrair preço atual
            price_selectors = [
                'span.a-price-whole',
                'span.a-price .a-offscreen',
                'span.a-price-range .a-offscreen',
                'span.a-price-fraction'
            ]

            price_text = None
            for selector in price_selectors:
                price_elem = product.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text(strip=True)
                    break

            price = self.clean_price(price_text) if price_text else 0.0

            # Extrair preço antigo (se houver)
            old_price_selectors = [
                'span.a-price.a-text-price .a-offscreen',
                'span.a-text-strike .a-offscreen',
                'span.a-price-was .a-offscreen'
            ]

            old_price_text = None
            for selector in old_price_selectors:
                old_price_elem = product.select_one(selector)
                if old_price_elem:
                    old_price_text = old_price_elem.get_text(strip=True)
                    break

            old_price = self.clean_price(old_price_text) if old_price_text else 0.0

            # Extrair link
            link_selectors = [
                'h3.a-size-base-plus a',
                'h2.a-size-mini a',
                'a.a-link-normal'
            ]

            link = None
            for selector in link_selectors:
                link_elem = product.select_one(selector)
                if link_elem:
                    href = link_elem.get('href')
                    if href:
                        if href.startswith('/'):
                            link = 'https://www.amazon.com.br' + href
                        else:
                            link = href
                        break

            # Extrair imagem
            image_selectors = [
                'img.s-image',
                'img[data-src]',
                'img[src]'
            ]

            image_url = None
            for selector in image_selectors:
                img_elem = product.select_one(selector)
                if img_elem:
                    image_url = img_elem.get('data-src') or img_elem.get('src')
                    if image_url and image_url.startswith('//'):
                        image_url = 'https:' + image_url
                    break

            # Calcular desconto
            discount = 0
            if old_price > 0 and price > 0:
                discount = round(100 - (price * 100 / old_price))

            return {
                'plataforma': 'Amazon',
                'titulo': title,
                'preco_atual': price,
                'preco_antigo': old_price,
                'desconto': discount,
                'url_produto': link,
                'url_afiliado': link,
                'url_imagem': image_url,
                'categoria': 'Pet Products',  # Categoria fixa por enquanto
                'ativo': True
            }

        except Exception as e:
            logger.error(f"Erro ao extrair dados do produto: {e}")
            return None

    def clean_price(self, price_text):
        """Limpa e converte texto de preço para float"""
        if not price_text:
            return 0.0

        try:
            # Remover caracteres não numéricos exceto vírgula e ponto
            import re
            price_clean = re.sub(r'[^\d,.]', '', price_text)

            # Tratar formato brasileiro (vírgula como decimal)
            if ',' in price_clean and '.' in price_clean:
                # Formato: 1.234,56
                price_clean = price_clean.replace('.', '').replace(',', '.')
            elif ',' in price_clean:
                # Formato: 1234,56
                price_clean = price_clean.replace(',', '.')

            return float(price_clean) if price_clean else 0.0

        except Exception:
            return 0.0

    def close(self):
        """Fecha o driver"""
        if self.driver:
            self.driver.quit()
            logger.info("Driver Selenium fechado")

def test_amazon_selenium():
    """Teste do scraper Selenium da Amazon"""
    scraper = None
    try:
        print("🔍 Testando Amazon com Selenium...")

        scraper = AmazonSeleniumScraper(headless=True)

        # URL de teste
        url = "https://www.amazon.com.br/deals"

        products = scraper.scrape_amazon_page(url, max_products=10)

        print(f"✅ Encontrados {len(products)} produtos")

        for i, product in enumerate(products[:3], 1):
            print(f"Produto {i}: {product['titulo'][:50]}... - R$ {product['preco_atual']}")

        return products

    except Exception as e:
        print(f"❌ Erro: {e}")
        return []
    finally:
        if scraper:
            scraper.close()

if __name__ == "__main__":
    test_amazon_selenium()
