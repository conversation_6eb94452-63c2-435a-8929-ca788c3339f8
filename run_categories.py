import time
import json
import logging
from twisted.internet import reactor, defer
from scrapy.crawler import <PERSON>rawler<PERSON><PERSON>ner
from scrapy.utils.project import get_project_settings
from scrapy.utils.log import configure_logging
from mercadolivre.mercadolivre.spiders.ml import MlSpider

# Configurar logging
logging.basicConfig(
    filename='logs/scrapy.log',
    format='%(asctime)s [%(name)s] %(levelname)s: %(message)s',
    level=logging.DEBUG
)

logger = logging.getLogger('CategoryRunner')

def get_categories_from_json(json_path):
    """Lê as categorias do arquivo JSON"""
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            logger.info(f"Categorias carregadas do JSON: {data}")
            return data
    except Exception as e:
        logger.error(f"Erro ao ler arquivo JSON: {e}")
        return []

@defer.inlineCallbacks
def crawl_sequentially():
    """Executa o spider para todas as categorias sequencialmente"""
    configure_logging()
    logger.info("Iniciando processo de crawling sequencial")
    
    # Obter categorias do JSON
    categories = get_categories_from_json('categories.json')
    logger.info(f"Encontradas {len(categories)} categorias para processar")

    # Criar runner
    settings = get_project_settings()
    settings.set("DOWNLOAD_DELAY", 3)
    runner = CrawlerRunner(settings)

    # Processar categorias
    for i, category in enumerate(categories, 1):
        category_name = category['name']
        category_file = f"output/{category_name.replace(' ', '_')}.csv"
        
        logger.info(f"Iniciando categoria {i}/{len(categories)}: {category_name}")
        logger.info(f"Arquivo de saída: {category_file}")

        # Configurar saída
        runner.settings.set("FEEDS", {
            category_file: {
                "format": "csv",
                "encoding": "utf-8",
                "overwrite": True
            }
        })

        try:
            logger.info(f"Iniciando crawler para categoria: {category_name}")
            yield runner.crawl(MlSpider, category_info=category)
            logger.info(f"Crawler finalizado para categoria: {category_name}")
            
        except Exception as e:
            logger.error(f"Erro ao processar categoria {category_name}: {e}")
            continue

        if i < len(categories):
            wait_time = 30
            logger.info(f"Aguardando {wait_time} segundos antes da próxima categoria")
            time.sleep(wait_time)

    logger.info("Processamento de todas as categorias concluído")
    reactor.stop()

def run_all_categories():
    """Função principal"""
    logger.info("Iniciando execução principal")
    try:
        crawl_sequentially()
        reactor.run()
    except Exception as e:
        logger.error(f"Erro na execução principal: {e}")

if __name__ == "__main__":
    run_all_categories()
