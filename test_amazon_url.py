#!/usr/bin/env python3
"""
Teste para verificar se conseguimos acessar e extrair produtos da URL da Amazon
"""

import requests
from bs4 import BeautifulSoup
import time

def test_amazon_url():
    """Testa se conseguimos acessar a URL da Amazon e extrair produtos"""
    
    # URL fornecida pelo usuário
    url = "https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-tools&promotionsSearchLastSeenAsin=B09N44Z1D6&promotionsSearchStartIndex=30&promotionsSearchPageSize=90"
    
    print(f"🔍 Testando URL: {url}")
    
    # Headers para simular um navegador real
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0'
    }
    
    try:
        print("📡 Fazendo requisição...")
        response = requests.get(url, headers=headers, timeout=30)
        print(f"✅ Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print(f"📄 Tamanho da resposta: {len(response.text)} caracteres")
            
            # Parse do HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Testar diferentes seletores para produtos
            selectors_to_test = [
                'div[data-testid][data-asin]',
                'div.ProductCard-module__card_uyr_Jh7WpSkPx4iEpn4w',
                'div.GridItem-module__container_PW2gdkwTj1GQzdwJjejN',
                'div.a-section.a-spacing-base',
                'div[data-asin]',
                'div.s-result-item',
                'div.a-section.a-spacing-small',
                'div.product-card',
                'div.deal-card'
            ]
            
            print("\n🔍 Testando seletores de produtos:")
            for selector in selectors_to_test:
                elements = soup.select(selector)
                print(f"  {selector}: {len(elements)} elementos encontrados")
                
                if elements and len(elements) > 0:
                    print(f"    ✅ Primeiro elemento: {elements[0].name} com {len(elements[0].attrs)} atributos")
                    # Mostrar alguns atributos
                    for attr, value in list(elements[0].attrs.items())[:3]:
                        print(f"      - {attr}: {str(value)[:50]}...")
            
            # Verificar se há produtos na página
            all_divs = soup.find_all('div')
            print(f"\n📊 Total de divs na página: {len(all_divs)}")
            
            # Procurar por elementos que podem conter produtos
            potential_products = soup.find_all('div', {'data-asin': True})
            print(f"📦 Elementos com data-asin: {len(potential_products)}")
            
            # Procurar por preços
            price_elements = soup.select('span.a-price, span.a-price-whole, span[class*="price"]')
            print(f"💰 Elementos de preço encontrados: {len(price_elements)}")
            
            # Procurar por títulos
            title_elements = soup.select('h1, h2, h3, h4, span[class*="title"], span[class*="truncate"]')
            print(f"📝 Elementos de título encontrados: {len(title_elements)}")
            
            # Salvar HTML para análise
            with open('amazon_page_debug.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print(f"💾 HTML salvo em 'amazon_page_debug.html' para análise")
            
            return True
            
        else:
            print(f"❌ Erro HTTP: {response.status_code}")
            print(f"📄 Resposta: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ Erro na requisição: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Iniciando teste da URL da Amazon...")
    success = test_amazon_url()
    
    if success:
        print("\n✅ Teste concluído com sucesso!")
        print("📋 Verifique o arquivo 'amazon_page_debug.html' para análise detalhada")
    else:
        print("\n❌ Teste falhou!")
