{"MercadoLivre": {"product": [{"id": 0, "type": "product", "selector": "li.ui-search-layout__item", "description": "Seletor para product", "active": true}, {"id": 1, "type": "product", "selector": "div.ui-search-result", "description": "Seletor para product", "active": true}, {"id": 2, "type": "product", "selector": "div.promotion-item", "description": "Seletor para product", "active": true}, {"id": 3, "type": "product", "selector": "div.andes-card.poly-card--grid-card", "description": "Seletor para product", "active": true}], "title": [{"id": 4, "type": "title", "selector": "h2.ui-search-item__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 5, "type": "title", "selector": "h2.promotion-item__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 6, "type": "title", "selector": "span.ui-search-item__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 7, "type": "title", "selector": "a.poly-component__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}], "price": [{"id": 8, "type": "price", "selector": "span.price-tag-amount", "description": "Seletor para price", "active": true}, {"id": 9, "type": "price", "selector": "span.ui-search-price__part", "description": "Seletor para price", "active": true}, {"id": 10, "type": "price", "selector": "span.promotion-item__price", "description": "Seletor para price", "active": true}, {"id": 11, "type": "price", "selector": "div.poly-price__current .andes-money-amount__fraction", "description": "Seletor para price", "active": true}, {"id": 12, "type": "price", "selector": "div.poly-price__current .andes-money-amount__cents", "description": "Seletor para price", "active": true}], "old_price": [{"id": 13, "type": "old_price", "selector": "s.andes-money-amount--previous .andes-money-amount__fraction", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 14, "type": "old_price", "selector": "span.andes-money-amount--previous .andes-money-amount__fraction", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 15, "type": "old_price", "selector": "span.price-tag-amount-previous", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}], "link": [{"id": 16, "type": "link", "selector": "a.ui-search-link", "description": "Se<PERSON>or para link", "active": true}, {"id": 17, "type": "link", "selector": "a.promotion-item__link", "description": "Se<PERSON>or para link", "active": true}, {"id": 18, "type": "link", "selector": "a.poly-component__title", "description": "Se<PERSON>or para link", "active": true}], "image": [{"id": 19, "type": "image", "selector": "img.ui-search-result-image__element", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 20, "type": "image", "selector": "img.promotion-item__img", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 21, "type": "image", "selector": "img[data-src]", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 22, "type": "image", "selector": "img.poly-component__picture", "description": "<PERSON><PERSON><PERSON> para image", "active": true}], "installments": [{"id": 23, "type": "installments", "selector": "span.poly-price__installments", "description": "Seletor para installments", "active": true}, {"id": 24, "type": "installments", "selector": "span.ui-search-installments", "description": "Seletor para installments", "active": true}, {"id": 25, "type": "installments", "selector": "span.promotion-item__installments", "description": "Seletor para installments", "active": true}], "coupon": [{"id": 26, "type": "coupon", "selector": "span.andes-money-amount__discount", "description": "Seletor para coupon", "active": true}, {"id": 27, "type": "coupon", "selector": "span.ui-search-price__discount", "description": "Seletor para coupon", "active": true}, {"id": 28, "type": "coupon", "selector": "span.promotion-item__discount", "description": "Seletor para coupon", "active": true}], "flash_deal": [{"id": 29, "type": "flash_deal", "selector": "span.poly-component__highlight:contains('OFERTA RELÂMPAGO')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}, {"id": 30, "type": "flash_deal", "selector": "span.poly-component__highlight:contains('Oferta relâmpago')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}, {"id": 31, "type": "flash_deal", "selector": "div.poly-component__tags span.poly-tags__pill:contains('Oferta relâmpago')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}], "bestseller": [{"id": 32, "type": "bestseller", "selector": "span.poly-component__highlight:contains('MAIS VENDIDO')", "description": "Se<PERSON>or para bestseller", "active": true}, {"id": 33, "type": "bestseller", "selector": "span.poly-component__highlight:contains('Mais vendido')", "description": "Se<PERSON>or para bestseller", "active": true}, {"id": 34, "type": "bestseller", "selector": "div.poly-component__tags span.poly-tags__pill:contains('Mais vendido')", "description": "Se<PERSON>or para bestseller", "active": true}], "recommended": [{"id": 35, "type": "recommended", "selector": "span.poly-component__highlight:contains('RECOMENDADO')", "description": "<PERSON><PERSON><PERSON> para recommended", "active": true}, {"id": 36, "type": "recommended", "selector": "span.poly-component__highlight svg[href='#poly_meli'] + :contains('RECOMENDADO')", "description": "<PERSON><PERSON><PERSON> para recommended", "active": true}]}, "Magalu": {"product": [{"id": 37, "type": "product", "selector": "a[data-testid=\"product-card-container\"]", "description": "Seletor para product", "active": true}, {"id": 38, "type": "product", "selector": "div.product-card", "description": "Seletor para product", "active": true}, {"id": 39, "type": "product", "selector": "div.product-item", "description": "Seletor para product", "active": true}, {"id": 40, "type": "product", "selector": "li.product-list-item", "description": "Seletor para product", "active": true}, {"id": 41, "type": "product", "selector": "div.productCard", "description": "Seletor para product", "active": true}, {"id": 42, "type": "product", "selector": "div.sc-dPiLbb", "description": "Seletor para product", "active": true}, {"id": 43, "type": "product", "selector": "div.sc-hKwDye", "description": "Seletor para product", "active": true}, {"id": 44, "type": "product", "selector": "div.sc-jrQzAO", "description": "Seletor para product", "active": true}, {"id": 45, "type": "product", "selector": "div.sc-kDTinF", "description": "Seletor para product", "active": true}, {"id": 46, "type": "product", "selector": "div.sc-iqseJM", "description": "Seletor para product", "active": true}], "title": [{"id": 47, "type": "title", "selector": "h2[data-testid=\"product-title\"]", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 48, "type": "title", "selector": "h2[data-testid=\"title\"]", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 49, "type": "title", "selector": "span[data-testid=\"title\"]", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 50, "type": "title", "selector": "div[data-testid=\"title\"]", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 51, "type": "title", "selector": "h3.product-title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 52, "type": "title", "selector": "h2.product-name", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 53, "type": "title", "selector": "div.product-card__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 54, "type": "title", "selector": "h2.sc-eCImPb", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 55, "type": "title", "selector": "h3.sc-gsDKAQ", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 56, "type": "title", "selector": "span.sc-hKwDye", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 57, "type": "title", "selector": "div.sc-jrQzAO", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 58, "type": "title", "selector": "span.sc-dtInlm", "description": "<PERSON><PERSON><PERSON> para title", "active": true}], "price": [{"id": 59, "type": "price", "selector": "p[data-testid=\"price-value\"]", "description": "Seletor para price", "active": true}, {"id": 60, "type": "price", "selector": "p[data-testid=\"installment\"]", "description": "Seletor para price", "active": true}, {"id": 61, "type": "price", "selector": "span.price-current", "description": "Seletor para price", "active": true}, {"id": 62, "type": "price", "selector": "div.product-price__current", "description": "Seletor para price", "active": true}, {"id": 63, "type": "price", "selector": "span.product-card__price", "description": "Seletor para price", "active": true}, {"id": 64, "type": "price", "selector": "p.sc-gsDKAQ", "description": "Seletor para price", "active": true}, {"id": 65, "type": "price", "selector": "span.sc-hKwDye", "description": "Seletor para price", "active": true}, {"id": 66, "type": "price", "selector": "div.sc-jrQzAO", "description": "Seletor para price", "active": true}, {"id": 67, "type": "price", "selector": "p.sc-eCImPb", "description": "Seletor para price", "active": true}, {"id": 68, "type": "price", "selector": "span[data-testid=\"price\"]", "description": "Seletor para price", "active": true}, {"id": 69, "type": "price", "selector": "p[data-testid=\"product-price\"]", "description": "Seletor para price", "active": true}, {"id": 70, "type": "price", "selector": "span.price", "description": "Seletor para price", "active": true}, {"id": 71, "type": "price", "selector": "div.price", "description": "Seletor para price", "active": true}, {"id": 72, "type": "price", "selector": "span.product-price", "description": "Seletor para price", "active": true}, {"id": 73, "type": "price", "selector": "div.product-price", "description": "Seletor para price", "active": true}, {"id": 74, "type": "price", "selector": "span.current-price", "description": "Seletor para price", "active": true}, {"id": 75, "type": "price", "selector": "div.current-price", "description": "Seletor para price", "active": true}, {"id": 76, "type": "price", "selector": "span.final-price", "description": "Seletor para price", "active": true}, {"id": 77, "type": "price", "selector": "div.final-price", "description": "Seletor para price", "active": true}], "old_price": [{"id": 78, "type": "old_price", "selector": "p[data-testid=\"price-original\"]", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 79, "type": "old_price", "selector": "span.price-old", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 80, "type": "old_price", "selector": "div.product-price__old", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 81, "type": "old_price", "selector": "span.product-card__old-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 82, "type": "old_price", "selector": "s.sc-gsDKAQ", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 83, "type": "old_price", "selector": "s.sc-hKwDye", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 84, "type": "old_price", "selector": "div.sc-jrQzAO s", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 85, "type": "old_price", "selector": "span[data-testid=\"old-price\"]", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 86, "type": "old_price", "selector": "p[data-testid=\"product-old-price\"]", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 87, "type": "old_price", "selector": "span.old-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 88, "type": "old_price", "selector": "div.old-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 89, "type": "old_price", "selector": "span.original-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 90, "type": "old_price", "selector": "div.original-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 91, "type": "old_price", "selector": "span.list-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 92, "type": "old_price", "selector": "div.list-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 93, "type": "old_price", "selector": "span.regular-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 94, "type": "old_price", "selector": "div.regular-price", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 95, "type": "old_price", "selector": "del", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}], "link": [{"id": 96, "type": "link", "selector": "a.product-card__link", "description": "Se<PERSON>or para link", "active": true}, {"id": 97, "type": "link", "selector": "a.product-item__link", "description": "Se<PERSON>or para link", "active": true}, {"id": 98, "type": "link", "selector": "a.product-list-item__link", "description": "Se<PERSON>or para link", "active": true}, {"id": 99, "type": "link", "selector": "a.sc-gsDKAQ", "description": "Se<PERSON>or para link", "active": true}, {"id": 100, "type": "link", "selector": "a.sc-hKwDye", "description": "Se<PERSON>or para link", "active": true}, {"id": 101, "type": "link", "selector": "a.sc-jrQzAO", "description": "Se<PERSON>or para link", "active": true}, {"id": 102, "type": "link", "selector": "a[data-testid=\"product-card-container\"]", "description": "Se<PERSON>or para link", "active": true}], "image": [{"id": 103, "type": "image", "selector": "img.product-card__image", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 104, "type": "image", "selector": "img.product-item__image", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 105, "type": "image", "selector": "img.product-list-item__image", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 106, "type": "image", "selector": "img.sc-gsDKAQ", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 107, "type": "image", "selector": "img.sc-hKwDye", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 108, "type": "image", "selector": "img.sc-jrQzAO", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 109, "type": "image", "selector": "img[data-testid=\"image\"]", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 110, "type": "image", "selector": "img[data-testid=\"product-image\"]", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 111, "type": "image", "selector": "img[data-testid=\"product-card-image\"]", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 112, "type": "image", "selector": "img", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 113, "type": "image", "selector": "picture img", "description": "<PERSON><PERSON><PERSON> para image", "active": true}], "coupon": [{"id": 114, "type": "coupon", "selector": "div[data-testid=\"productCard-coupon\"]", "description": "Seletor para coupon", "active": true}, {"id": 115, "type": "coupon", "selector": "p:contains(\"Cupom\")", "description": "Seletor para coupon", "active": true}, {"id": 116, "type": "coupon", "selector": "div.sc-hqpNSm", "description": "Seletor para coupon", "active": true}, {"id": 117, "type": "coupon", "selector": "div.bxDFiO", "description": "Seletor para coupon", "active": true}], "installments": [{"id": 118, "type": "installments", "selector": "span.poly-price__installments", "description": "Seletor para installments", "active": true}, {"id": 119, "type": "installments", "selector": "span.ui-search-installments", "description": "Seletor para installments", "active": true}, {"id": 120, "type": "installments", "selector": "span.promotion-item__installments", "description": "Seletor para installments", "active": true}], "flash_deal": [{"id": 121, "type": "flash_deal", "selector": "span.poly-component__highlight:contains('OFERTA RELÂMPAGO')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}, {"id": 122, "type": "flash_deal", "selector": "span.poly-component__highlight:contains('Oferta relâmpago')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}, {"id": 123, "type": "flash_deal", "selector": "div.poly-component__tags span.poly-tags__pill:contains('Oferta relâmpago')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}], "bestseller": [{"id": 124, "type": "bestseller", "selector": "span.poly-component__highlight:contains('MAIS VENDIDO')", "description": "Se<PERSON>or para bestseller", "active": true}, {"id": 125, "type": "bestseller", "selector": "span.poly-component__highlight:contains('Mais vendido')", "description": "Se<PERSON>or para bestseller", "active": true}, {"id": 126, "type": "bestseller", "selector": "div.poly-component__tags span.poly-tags__pill:contains('Mais vendido')", "description": "Se<PERSON>or para bestseller", "active": true}], "recommended": [{"id": 127, "type": "recommended", "selector": "span.poly-component__highlight:contains('RECOMENDADO')", "description": "<PERSON><PERSON><PERSON> para recommended", "active": true}, {"id": 128, "type": "recommended", "selector": "span.poly-component__highlight svg[href='#poly_meli'] + :contains('RECOMENDADO')", "description": "<PERSON><PERSON><PERSON> para recommended", "active": true}]}, "Amazon": {"product": [{"id": 129, "type": "product", "selector": "li.ui-search-layout__item", "description": "Seletor para product", "active": true}, {"id": 130, "type": "product", "selector": "div.ui-search-result", "description": "Seletor para product", "active": true}, {"id": 131, "type": "product", "selector": "div.promotion-item", "description": "Seletor para product", "active": true}, {"id": 132, "type": "product", "selector": "div.andes-card.poly-card--grid-card", "description": "Seletor para product", "active": true}], "title": [{"id": 133, "type": "title", "selector": "h2.ui-search-item__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 134, "type": "title", "selector": "h2.promotion-item__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 135, "type": "title", "selector": "span.ui-search-item__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}, {"id": 136, "type": "title", "selector": "a.poly-component__title", "description": "<PERSON><PERSON><PERSON> para title", "active": true}], "price": [{"id": 137, "type": "price", "selector": "span.price-tag-amount", "description": "Seletor para price", "active": true}, {"id": 138, "type": "price", "selector": "span.ui-search-price__part", "description": "Seletor para price", "active": true}, {"id": 139, "type": "price", "selector": "span.promotion-item__price", "description": "Seletor para price", "active": true}, {"id": 140, "type": "price", "selector": "div.poly-price__current .andes-money-amount__fraction", "description": "Seletor para price", "active": true}, {"id": 141, "type": "price", "selector": "div.poly-price__current .andes-money-amount__cents", "description": "Seletor para price", "active": true}], "old_price": [{"id": 142, "type": "old_price", "selector": "s.andes-money-amount--previous .andes-money-amount__fraction", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 143, "type": "old_price", "selector": "span.andes-money-amount--previous .andes-money-amount__fraction", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}, {"id": 144, "type": "old_price", "selector": "span.price-tag-amount-previous", "description": "<PERSON><PERSON><PERSON> para old_price", "active": true}], "link": [{"id": 145, "type": "link", "selector": "a.ui-search-link", "description": "Se<PERSON>or para link", "active": true}, {"id": 146, "type": "link", "selector": "a.promotion-item__link", "description": "Se<PERSON>or para link", "active": true}, {"id": 147, "type": "link", "selector": "a.poly-component__title", "description": "Se<PERSON>or para link", "active": true}], "image": [{"id": 148, "type": "image", "selector": "img.ui-search-result-image__element", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 149, "type": "image", "selector": "img.promotion-item__img", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 150, "type": "image", "selector": "img[data-src]", "description": "<PERSON><PERSON><PERSON> para image", "active": true}, {"id": 151, "type": "image", "selector": "img.poly-component__picture", "description": "<PERSON><PERSON><PERSON> para image", "active": true}], "installments": [{"id": 152, "type": "installments", "selector": "span.poly-price__installments", "description": "Seletor para installments", "active": true}, {"id": 153, "type": "installments", "selector": "span.ui-search-installments", "description": "Seletor para installments", "active": true}, {"id": 154, "type": "installments", "selector": "span.promotion-item__installments", "description": "Seletor para installments", "active": true}], "coupon": [{"id": 155, "type": "coupon", "selector": "span.andes-money-amount__discount", "description": "Seletor para coupon", "active": true}, {"id": 156, "type": "coupon", "selector": "span.ui-search-price__discount", "description": "Seletor para coupon", "active": true}, {"id": 157, "type": "coupon", "selector": "span.promotion-item__discount", "description": "Seletor para coupon", "active": true}], "flash_deal": [{"id": 158, "type": "flash_deal", "selector": "span.poly-component__highlight:contains('OFERTA RELÂMPAGO')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}, {"id": 159, "type": "flash_deal", "selector": "span.poly-component__highlight:contains('Oferta relâmpago')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}, {"id": 160, "type": "flash_deal", "selector": "div.poly-component__tags span.poly-tags__pill:contains('Oferta relâmpago')", "description": "<PERSON><PERSON><PERSON> para flash_deal", "active": true}], "bestseller": [{"id": 161, "type": "bestseller", "selector": "span.poly-component__highlight:contains('MAIS VENDIDO')", "description": "Se<PERSON>or para bestseller", "active": true}, {"id": 162, "type": "bestseller", "selector": "span.poly-component__highlight:contains('Mais vendido')", "description": "Se<PERSON>or para bestseller", "active": true}, {"id": 163, "type": "bestseller", "selector": "div.poly-component__tags span.poly-tags__pill:contains('Mais vendido')", "description": "Se<PERSON>or para bestseller", "active": true}], "recommended": [{"id": 164, "type": "recommended", "selector": "span.poly-component__highlight:contains('RECOMENDADO')", "description": "<PERSON><PERSON><PERSON> para recommended", "active": true}, {"id": 165, "type": "recommended", "selector": "span.poly-component__highlight svg[href='#poly_meli'] + :contains('RECOMENDADO')", "description": "<PERSON><PERSON><PERSON> para recommended", "active": true}]}}