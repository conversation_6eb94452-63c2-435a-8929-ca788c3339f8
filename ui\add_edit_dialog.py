import tkinter as tk
from tkinter import ttk, messagebox

class AddEditCategoryDialog:
    def __init__(self, parent, title, initial_values=None, store_manager=None):
        self.parent = parent
        self.result = None

        # Obter o gerenciador de lojas do parent (CategoryFrame)
        if hasattr(parent, 'store_manager'):
            self.store_manager = parent.store_manager
        else:
            self.store_manager = store_manager

        # Obter a loja atual
        self.current_store = self.store_manager.get_current_store() if self.store_manager else "MercadoLivre"

        # Criar janela
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("700x300")  # Aumentar o tamanho da janela
        self.dialog.minsize(600, 300)    # Definir tamanho mínimo
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Configurar grid para ser responsivo
        self.dialog.columnconfigure(1, weight=1)

        # Frame principal
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        main_frame.columnconfigure(1, weight=1)  # Coluna da entrada expande

        # Campos do formulário
        ttk.Label(main_frame, text="Nome da Categoria:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=10)
        self.name_entry = ttk.Entry(main_frame, width=40)
        self.name_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=10, pady=10)

        ttk.Label(main_frame, text="URL Template:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=10)
        self.url_entry = ttk.Entry(main_frame, width=40)
        self.url_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=10, pady=10)

        # Exemplo de URL baseado na loja atual
        if self.current_store == "MercadoLivre":
            example_url = "Ex: https://www.mercadolivre.com.br/ofertas?page={i}"
        elif self.current_store == "Magalu":
            example_url = "Ex: https://www.magazinevoce.com.br/magazinepromobelloficial/casa-e-construcao/l/cj/?page={i}"
        else:
            example_url = "Ex: https://www.mercadolivre.com.br/ofertas?page={i}"

        self.example_label = ttk.Label(main_frame, text=example_url, wraplength=500)  # Adicionar wraplength para quebrar texto longo
        self.example_label.grid(row=2, column=1, sticky=tk.W, padx=10)

        ttk.Label(main_frame, text="Páginas (max_page):").grid(row=3, column=0, sticky=tk.W, padx=10, pady=10)
        self.max_page_entry = ttk.Entry(main_frame, width=10)
        self.max_page_entry.grid(row=3, column=1, sticky=tk.W, padx=10, pady=10)

        # Preencher valores iniciais, se fornecidos
        if initial_values:
            name, url, max_page = initial_values
            self.name_entry.insert(0, name)
            self.url_entry.insert(0, url)
            self.max_page_entry.insert(0, str(max_page))

        # Botões
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=4, column=0, columnspan=2, pady=20)

        ttk.Button(btn_frame, text="Salvar", command=self.save).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="Cancelar", command=self.cancel).pack(side=tk.LEFT, padx=10)

        # Esperar até o diálogo ser fechado
        self.dialog.wait_window()

    def save(self):
        name = self.name_entry.get().strip()
        url = self.url_entry.get().strip()
        max_page = self.max_page_entry.get().strip()

        if not name or not url or not max_page:
            messagebox.showerror("Erro", "Todos os campos são obrigatórios", parent=self.dialog)
            return

        try:
            max_page = int(max_page)
            if max_page <= 0:
                raise ValueError("Número de páginas deve ser maior que zero")

            self.result = (name, url, max_page)
            self.dialog.destroy()
        except ValueError as e:
            messagebox.showerror("Erro", f"Valor inválido: {str(e)}", parent=self.dialog)

    def cancel(self):
        self.dialog.destroy()
