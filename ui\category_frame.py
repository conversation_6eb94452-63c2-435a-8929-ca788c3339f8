import tkinter as tk
from tkinter import ttk, messagebox
from .add_edit_dialog import AddEditCategoryDialog
import json
import os
from utils.store_manager import StoreManager

class CategoryFrame(ttk.Frame):
    def __init__(self, master, category_manager, status_callback=None, store_manager=None):
        super().__init__(master, padding="10")
        self.category_manager = category_manager
        self.status_callback = status_callback

        # Usa a instância de StoreManager recebida ou cria uma nova
        self.store_manager = store_manager if store_manager else StoreManager()

        # Configuração para salvamento de tamanhos de colunas
        self.column_config_file = "column_sizes.json"
        self.column_sizes = self.load_column_sizes()

        # Criar componentes
        self.create_widgets()
        self.load_categories()

    def on_store_change(self, event=None):
        """Handler para mudança na seleção da loja"""
        selected_store = self.store_var.get()
        self.store_manager.set_current_store(selected_store)
        if self.status_callback:
            self.status_callback(f"Loja alterada para: {selected_store}")
        self.load_categories()
        self.focus_set()

    def create_widgets(self):
        # Frame de configuração da loja
        store_frame = ttk.Frame(self)
        store_frame.pack(fill=tk.X, pady=5)

        # Seleção de loja
        ttk.Label(store_frame, text="Selecione a Loja:").pack(side="left", padx=(0, 5))

        self.store_var = tk.StringVar(value=self.store_manager.get_current_store())
        self.store_dropdown = ttk.Combobox(
            store_frame,
            textvariable=self.store_var,
            values=self.store_manager.get_store_names(),
            state="readonly",
            width=20
        )
        self.store_dropdown.pack(side="left")
        self.store_dropdown.bind("<<ComboboxSelected>>", self.on_store_change)

        # Botões de controle
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="Adicionar Categoria",
                command=self.add_category).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Atualizar Lista",
                command=self.load_categories).pack(side=tk.LEFT, padx=5)

        # Tabela de categorias
        self.tree_frame = ttk.Frame(self)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        columns = ("id", "name", "url", "max_page")
        self.tree = ttk.Treeview(self.tree_frame, columns=columns, show="headings")
        self.tree.heading("id", text="ID")
        self.tree.heading("name", text="Categoria")
        self.tree.heading("url", text="URL")
        self.tree.heading("max_page", text="Páginas")

        # Aplicar tamanhos de coluna
        self.tree.column("id", width=self.column_sizes.get("id", 50), stretch=False)
        self.tree.column("name", width=self.column_sizes.get("name", 150))
        self.tree.column("url", width=self.column_sizes.get("url", 400))
        self.tree.column("max_page", width=self.column_sizes.get("max_page", 70), anchor=tk.CENTER)

        # Vincular evento de seleção
        self.tree.bind('<<TreeviewSelect>>', self.on_item_select)

        scrollbar = ttk.Scrollbar(self.tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Painel de ações abaixo da tabela
        self.action_frame = ttk.LabelFrame(self, text="Ações para o item selecionado")
        self.action_frame.pack(fill=tk.X, pady=10, padx=5)

        # Criar botões de ação
        self.edit_btn = ttk.Button(self.action_frame, text="Editar Categoria",
                                command=self.edit_selected, state=tk.DISABLED, width=20)
        self.edit_btn.pack(side=tk.LEFT, padx=20, pady=10)

        self.delete_btn = ttk.Button(self.action_frame, text="Excluir Categoria",
                                command=self.delete_selected, state=tk.DISABLED, width=20)
        self.delete_btn.pack(side=tk.LEFT, padx=20, pady=10)

        # Botão para excluir múltiplas categorias
        self.delete_selected_btn = ttk.Button(self.action_frame, text="Excluir Selecionadas",
                                           command=self.delete_multiple_selected,
                                           state=tk.NORMAL, width=20)
        self.delete_selected_btn.pack(side=tk.LEFT, padx=20, pady=10)

    def on_item_select(self, event):
        # Ativar botões quando um item é selecionado
        selected = self.tree.selection()
        if selected:
            self.edit_btn.config(state=tk.NORMAL)
            self.delete_btn.config(state=tk.NORMAL)

            # Opcional: mostrar informações adicionais do item selecionado
            item_values = self.tree.item(selected[0], "values")
            self.action_frame.config(text=f"Ações para: {item_values[1]}")  # Mostrar nome da categoria
        else:
            self.edit_btn.config(state=tk.DISABLED)
            self.delete_btn.config(state=tk.DISABLED)
            self.action_frame.config(text="Ações para o item selecionado")

    def on_column_resize(self, event):
        # Verificar se o clique foi em um cabeçalho
        if event.widget.identify_region(event.x, event.y) == "separator":
            # Atualizar tamanhos
            for col in ("id", "name", "url", "max_page", "edit", "delete"):
                self.column_sizes[col] = self.tree.column(col, "width")

            # Salvar configurações
            self.save_column_sizes()

    def load_column_sizes(self):
        """Carrega tamanhos salvos de colunas"""
        if os.path.exists(self.column_config_file):
            try:
                with open(self.column_config_file, "r") as f:
                    return json.load(f)
            except:
                return {}
        return {}

    def save_column_sizes(self):
        """Salva tamanhos das colunas em arquivo"""
        try:
            with open(self.column_config_file, "w") as f:
                json.dump(self.column_sizes, f)
        except Exception as e:
            print(f"Erro ao salvar tamanhos de colunas: {e}")

    def handle_action_click(self, event, cat_id):
        # Identificar em qual coluna o clique ocorreu
        column = self.tree.identify_column(event.x)

        if column == "#5":  # Coluna de edição
            self.edit_category(cat_id)
        elif column == "#6":  # Coluna de exclusão
            self.delete_category(cat_id)

    def load_categories(self):
        # Limpar tabela
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            # Carregar categorias diretamente do JSON
            categories = self.category_manager.get_all_categories()

            if not categories:
                if self.status_callback:
                    self.status_callback("Nenhuma categoria encontrada")
                return

            # Adicionar categorias à tabela
            for category in categories:
                self.tree.insert("", "end", values=(
                    category['id'],
                    category['name'],
                    category['url_template'],
                    category['max_page']
                ))

            # Atualizar status
            if self.status_callback:
                self.status_callback(f"{len(categories)} categorias carregadas")

            # Desabilitar botões de ação até que um item seja selecionado
            self.edit_btn.config(state=tk.DISABLED)
            self.delete_btn.config(state=tk.DISABLED)

            # Habilitar o botão de exclusão múltipla se houver categorias
            if categories:
                self.delete_selected_btn.config(state=tk.NORMAL)
            else:
                self.delete_selected_btn.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar categorias: {e}")
            if self.status_callback:
                self.status_callback("Erro ao carregar categorias")



    def get_selected_category_id(self):
        """Retorna o ID da categoria selecionada ou None"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("Aviso", "Por favor, selecione uma categoria")
            return None

        # Obter o ID da categoria selecionada
        values = self.tree.item(selected[0], "values")
        return values[0] if values else None

    def add_category(self):
        dialog = AddEditCategoryDialog(self, "Adicionar Categoria", store_manager=self.store_manager)
        if dialog.result:
            name, url_template, max_page = dialog.result
            if self.category_manager.add_category(name, url_template, max_page):
                messagebox.showinfo("Sucesso", "Categoria adicionada com sucesso")
                self.load_categories()
            else:
                messagebox.showerror("Erro", "Categoria já existe")

    def edit_selected(self):
        cat_id = self.get_selected_category_id()
        if cat_id:
            self.edit_category(cat_id)

    def edit_category(self, cat_id):
        # Encontrar a categoria na lista de categorias
        categories = self.category_manager.get_all_categories()
        category = next((cat for cat in categories if cat['id'] == int(cat_id)), None)

        if not category:
            messagebox.showerror("Erro", "Categoria não encontrada")
            return

        dialog = AddEditCategoryDialog(self, f"Editar: {category['name']}",
                                     initial_values=(category['name'],
                                                   category['url_template'],
                                                   category['max_page']),
                                     store_manager=self.store_manager)

        if dialog.result:
            name, url_template, max_page = dialog.result
            # Atualizar categoria usando o CategoryManager
            if self.category_manager.update_category(cat_id, name, url_template, max_page):
                messagebox.showinfo("Sucesso", "Categoria atualizada com sucesso")
                self.load_categories()
            else:
                messagebox.showerror("Erro", "Erro ao atualizar categoria")

    def delete_selected(self):
        cat_id = self.get_selected_category_id()
        if cat_id:
            self.delete_category(cat_id)

    def delete_category(self, cat_id):
        category = self.category_manager.get_category(cat_id)
        if not category:
            messagebox.showerror("Erro", "Categoria não encontrada")
            return

        confirm = messagebox.askyesno("Confirmar Exclusão",
                                     f"Tem certeza que deseja excluir a categoria '{category[1]}'?")
        if confirm:
            if self.category_manager.delete_category(cat_id):
                messagebox.showinfo("Sucesso", "Categoria excluída com sucesso")
                self.load_categories()
            else:
                messagebox.showerror("Erro", "Erro ao excluir categoria")

    def delete_multiple_selected(self):
        """Exclui todas as categorias selecionadas"""
        # Obter itens selecionados
        selected_items = self.tree.selection()

        if not selected_items:
            messagebox.showinfo("Aviso", "Nenhuma categoria selecionada")
            return

        # Obter IDs e nomes das categorias selecionadas
        selected_ids = []
        selected_names = []

        for item_id in selected_items:
            values = self.tree.item(item_id, "values")
            cat_id = values[0]  # ID da categoria
            cat_name = values[1]  # Nome da categoria

            selected_ids.append(cat_id)
            selected_names.append(cat_name)

        # Confirmar exclusão
        confirm_message = f"Tem certeza que deseja excluir as {len(selected_ids)} categorias selecionadas?\n\n"

        # Limitar a quantidade de nomes exibidos para não sobrecarregar a mensagem
        if len(selected_names) <= 5:
            confirm_message += "\n".join(f"- {name}" for name in selected_names)
        else:
            confirm_message += "\n".join(f"- {name}" for name in selected_names[:5])
            confirm_message += f"\n... e mais {len(selected_names) - 5} categorias"

        confirm = messagebox.askyesno("Confirmar Exclusão Múltipla", confirm_message)

        if confirm:
            if self.category_manager.delete_multiple_categories(selected_ids):
                messagebox.showinfo("Sucesso", f"{len(selected_ids)} categorias excluídas com sucesso")
                self.load_categories()
            else:
                messagebox.showerror("Erro", "Erro ao excluir categorias")
