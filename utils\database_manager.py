import json
from pathlib import Path

class DatabaseManager:
    def __init__(self, data_dir="data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        self.categories_file = self.data_dir / "categories.json"
        
        if not self.categories_file.exists():
            self._save_categories([])

    def _load_categories(self):
        """Carrega categorias do arquivo JSON"""
        try:
            with open(self.categories_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Erro ao carregar categorias: {e}")
            return []

    def _save_categories(self, categories):
        """Salva categorias no arquivo JSON"""
        try:
            with open(self.categories_file, 'w', encoding='utf-8') as f:
                json.dump(categories, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"Erro ao salvar categorias: {e}")
            return False

    def add_category(self, name, url_template, max_page):
        """Adiciona uma nova categoria"""
        categories = self._load_categories()
        
        if any(c['name'] == name for c in categories):
            return False
        
        categories.append({
            'name': name,
            'url_template': url_template,
            'max_page': max_page
        })
        
        return self._save_categories(categories)

    def get_all_categories(self):
        """Retorna todas as categorias"""
        return self._load_categories()