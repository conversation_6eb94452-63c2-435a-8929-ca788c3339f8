#!/usr/bin/env python3

import sys
import logging
sys.path.append('.')

# Configurar logging para ver tudo
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s:%(name)s:%(message)s')

from src.scrapers.amazon_selenium import AmazonSeleniumScraper

def debug_selenium():
    print('🔍 Debug detalhado do Selenium...')
    
    scraper = AmazonSeleniumScraper(headless=True)
    
    try:
        url = 'https://www.amazon.com.br/deals'
        products = scraper.scrape_amazon_page(url, max_products=3)
        
        print(f'\n📊 RESULTADOS:')
        for i, product in enumerate(products, 1):
            print(f'Produto {i}:')
            print(f'  Título: {product.get("titulo", "N/A")}')
            print(f'  Preço atual: R$ {product.get("preco_atual", 0)}')
            print(f'  Preço antigo: R$ {product.get("preco_antigo", 0)}')
            print(f'  Desconto: {product.get("desconto", 0)}%')
            link = product.get("url_produto", "N/A")
            print(f'  Link: {link[:50] if link else "N/A"}...')
            print()
        
        return products
        
    except Exception as e:
        print(f'❌ Erro: {e}')
        import traceback
        traceback.print_exc()
        return []
    finally:
        scraper.close()

if __name__ == "__main__":
    debug_selenium()
