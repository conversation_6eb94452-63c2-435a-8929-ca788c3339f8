import tkinter as tk
from tkinter import ttk, messagebox
from utils.store_manager import StoreManager

class SelectorAddEditDialog(tk.Toplevel):
    def __init__(self, parent, title, callback, initial_values=None, selector_types=None):
        super().__init__(parent)
        self.title(title)
        self.callback = callback
        self.result = None

        # Configurar como modal
        self.transient(parent)
        self.grab_set()

        # Usar lista padrão se nenhuma for fornecida
        self.selector_types = selector_types or ['product', 'title', 'price', 'old_price', 'link', 'image', 'installments', 'coupon']

        # Desempacotar valores iniciais com segurança
        if initial_values:
            selector_id, selector_type, selector, description, active, _ = initial_values
            active = bool(active)
        else:
            selector_type = ""
            selector = ""
            description = ""
            active = True

        # Criar widgets
        self.create_widgets(selector_type, selector, description, active)

        # Configurar protocolo de fechamento
        self.protocol("WM_DELETE_WINDOW", self.on_cancel)

        # Centralizar a janela
        self.geometry("+%d+%d" % (parent.winfo_rootx() + 50,
                                 parent.winfo_rooty() + 50))

        # Focar na janela
        self.focus_set()

    def create_widgets(self, selector_type, selector, description, active):
        # Frame principal
        main_frame = ttk.Frame(self, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Widgets
        ttk.Label(main_frame, text="Tipo:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.type_combo = ttk.Combobox(main_frame, values=self.selector_types, state="readonly")
        self.type_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        if selector_type:
            self.type_combo.set(selector_type)

        ttk.Label(main_frame, text="Seletor:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.selector_entry = ttk.Entry(main_frame, width=40)
        self.selector_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        self.selector_entry.insert(0, selector)

        ttk.Label(main_frame, text="Descrição:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.description_entry = ttk.Entry(main_frame, width=40)
        self.description_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        self.description_entry.insert(0, description)

        self.active_var = tk.BooleanVar(value=active)
        self.active_check = ttk.Checkbutton(main_frame, text="Ativo", variable=self.active_var)
        self.active_check.grid(row=3, column=0, columnspan=2, pady=10)

        # Botões
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="OK", command=self.on_ok).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancelar", command=self.on_cancel).pack(side=tk.LEFT, padx=5)

    def on_ok(self):
        # Validar entradas
        if not self.type_combo.get() or not self.selector_entry.get().strip():
            messagebox.showwarning("Aviso", "Por favor, preencha todos os campos obrigatórios")
            return

        self.result = (
            self.type_combo.get(),
            self.selector_entry.get().strip(),
            self.description_entry.get().strip(),
            self.active_var.get()
        )
        self.destroy()

    def on_cancel(self):
        self.result = None
        self.destroy()

class SelectorFrame(ttk.Frame):
    def __init__(self, master, selector_manager, status_callback=None, store_manager=None):
        super().__init__(master, padding="10")
        self.selector_manager = selector_manager
        self.status_callback = status_callback

        # Usa a instância de StoreManager recebida ou cria uma nova
        self.store_manager = store_manager if store_manager else StoreManager()

        # Tipos de seletores disponíveis
        self.selector_types = ['product', 'title', 'price', 'old_price', 'link', 'image', 'installments', 'coupon']

        # Criar componentes
        self.create_widgets()
        self.load_selectors()

    def on_store_change(self, event=None):
        """Handler para mudança na seleção da loja"""
        selected_store = self.store_var.get()
        self.store_manager.set_current_store(selected_store)
        if self.status_callback:
            self.status_callback(f"Loja alterada para: {selected_store}")
        self.load_selectors()
        self.focus_set()

    def create_widgets(self):
        # Frame de configuração da loja
        store_frame = ttk.Frame(self)
        store_frame.pack(fill=tk.X, pady=5)

        # Seleção de loja
        ttk.Label(store_frame, text="Selecione a Loja:").pack(side="left", padx=(0, 5))

        self.store_var = tk.StringVar(value=self.store_manager.get_current_store())
        self.store_dropdown = ttk.Combobox(
            store_frame,
            textvariable=self.store_var,
            values=self.store_manager.get_store_names(),
            state="readonly",
            width=20
        )
        self.store_dropdown.pack(side="left")
        self.store_dropdown.bind("<<ComboboxSelected>>", self.on_store_change)

        # Botões de controle
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, pady=5)

        ttk.Button(control_frame, text="Adicionar Seletor",
                  command=self.add_selector).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Atualizar Lista",
                  command=self.load_selectors).pack(side=tk.LEFT, padx=5)

        # Filtro de tipo
        filter_frame = ttk.Frame(self)
        filter_frame.pack(fill=tk.X, pady=5)

        ttk.Label(filter_frame, text="Filtrar por tipo:").pack(side=tk.LEFT, padx=5)

        self.filter_var = tk.StringVar(value="Todos")
        filter_values = ["Todos"] + self.selector_types
        self.filter_combo = ttk.Combobox(filter_frame, values=filter_values,
                                       textvariable=self.filter_var, state="readonly", width=15)
        self.filter_combo.pack(side=tk.LEFT, padx=5)
        self.filter_combo.bind("<<ComboboxSelected>>", lambda e: self.load_selectors())

        # Tabela de seletores
        self.tree_frame = ttk.Frame(self)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        columns = ("type", "selector", "description", "active")
        self.tree = ttk.Treeview(self.tree_frame, columns=columns, show="headings")
        self.tree.heading("type", text="Tipo")
        self.tree.heading("selector", text="Seletor CSS")
        self.tree.heading("description", text="Descrição")
        self.tree.heading("active", text="Ativo")

        self.tree.column("type", width=100)
        self.tree.column("selector", width=250)
        self.tree.column("description", width=300)
        self.tree.column("active", width=70, anchor=tk.CENTER)

        scrollbar = ttk.Scrollbar(self.tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Frame de botões de ação
        action_frame = ttk.Frame(self)
        action_frame.pack(fill=tk.X, pady=5)

        ttk.Button(action_frame, text="Editar Selecionado",
                  command=self.edit_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="Excluir Selecionado",
                  command=self.delete_selected).pack(side=tk.LEFT, padx=5)

    def load_selectors(self):
        # Limpar tabela
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            # Carregar seletores
            filter_type = self.filter_var.get()
            if filter_type == "Todos":
                selectors = self.selector_manager.get_all_selectors()
            else:
                selectors = self.selector_manager.get_selectors_by_type(filter_type)

            # Adicionar à tabela
            for index, selector in enumerate(selectors):
                # Verificar se temos todos os campos necessários
                if len(selector) >= 6:  # Considerando que temos 6 campos no total
                    selector_id = selector[0]
                    selector_type = selector[1]
                    selector_text = selector[2]
                    description = selector[3]
                    active = selector[4]
                    # created_at = selector[5]  # Se precisar usar depois
                else:
                    # Log do erro e continue para o próximo
                    print(f"Dados incompletos para o seletor: {selector}")
                    continue

                # Formatar o texto "Ativo"
                active_text = "Sim" if active == 1 else "Não"

                # Adicionar à tabela com o id real como tag
                self.tree.insert("", index,
                               values=(selector_type, selector_text, description, active_text),
                               tags=(str(selector_id),))

            # Atualizar status
            if self.status_callback:
                self.status_callback(f"{len(selectors)} seletores carregados")

        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao carregar seletores: {str(e)}")
            if self.status_callback:
                self.status_callback("Erro ao carregar seletores")

    def get_selected_selector_id(self):
        """Retorna o ID do seletor selecionado ou None"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("Aviso", "Por favor, selecione um seletor")
            return None

        # Obter o ID do seletor da tag
        return self.tree.item(selected[0], "tags")[0]

    def add_selector(self):
        """Abre diálogo para adicionar novo seletor"""
        dialog = SelectorAddEditDialog(
            self,
            "Adicionar Seletor",
            self.handle_add,
            selector_types=self.selector_types
        )

        # Esperar até que o diálogo seja fechado
        self.wait_window(dialog)

        # Processar resultado apenas se houver um
        if dialog.result:
            self.handle_add(dialog.result)

    def handle_add(self, values):
        """Manipula a adição de um novo seletor"""
        try:
            selector_type, selector_text, description, active = values
            success = self.selector_manager.add_selector(
                selector_type,
                selector_text,
                description,
                active
            )

            if success:
                self.load_selectors()  # Recarrega a lista
                if self.status_callback:
                    self.status_callback("Seletor adicionado com sucesso")
            else:
                messagebox.showerror("Erro", "Seletor já existe para este tipo")
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao adicionar seletor: {str(e)}")

    def edit_selected(self):
        """Edita o seletor selecionado"""
        selected_item = self.tree.selection()
        if not selected_item:
            messagebox.showwarning("Aviso", "Por favor, selecione um seletor para editar")
            return

        item_values = self.tree.item(selected_item)['values']
        selector_id = self.tree.item(selected_item)['tags'][0]

        initial_values = (
            int(selector_id),
            item_values[0],    # type
            item_values[1],    # selector
            item_values[2],    # description
            1 if item_values[3] == "Sim" else 0,  # active
            None              # placeholder
        )

        dialog = SelectorAddEditDialog(
            self,
            "Editar Seletor",
            lambda values: self.handle_edit(selector_id, values),
            initial_values,
            selector_types=self.selector_types
        )

        # Esperar até que o diálogo seja fechado
        self.wait_window(dialog)

        # Processar resultado apenas se houver um
        if dialog.result:
            self.handle_edit(selector_id, dialog.result)

    def handle_edit(self, selector_id, values):
        """Manipula a edição de um seletor"""
        try:
            selector_type, selector_text, description, active = values
            success = self.selector_manager.update_selector(
                int(selector_id),
                selector_type,
                selector_text,
                description,
                active
            )

            if success:
                self.load_selectors()  # Recarrega a lista
                if self.status_callback:
                    self.status_callback("Seletor atualizado com sucesso")
            else:
                messagebox.showerror("Erro", "Não foi possível atualizar o seletor")
        except Exception as e:
            messagebox.showerror("Erro", f"Erro ao atualizar seletor: {str(e)}")

    def delete_selected(self):
        selector_id = self.get_selected_selector_id()
        if selector_id:
            # Confirmar exclusão
            confirm = messagebox.askyesno(
                "Confirmar Exclusão",
                "Tem certeza que deseja excluir este seletor?"
            )

            if confirm:
                if self.selector_manager.delete_selector(int(selector_id)):
                    messagebox.showinfo("Sucesso", "Seletor excluído com sucesso")
                    self.load_selectors()
                else:
                    messagebox.showerror("Erro", "Erro ao excluir seletor")
