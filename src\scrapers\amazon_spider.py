"""
Spider da Amazon para o Promohunter
"""

import scrapy
import logging
import re
from datetime import datetime
from urllib.parse import urlparse, parse_qs, urlenco<PERSON>, urlunparse

logger = logging.getLogger(__name__)

class AmazonSpider(scrapy.Spider):
    name = "amazon"
    allowed_domains = ["amazon.com.br", "amazon.com"]

    def __init__(self, url=None, *args, **kwargs):
        super(AmazonSpider, self).__init__(*args, **kwargs)
        self.start_urls = [url] if url else []
        self.logger.info(f"Iniciando spider para URL: {url}")

    def parse(self, response):
        self.logger.info(f"Processando página: {response.url}")

        # Extrai o título do produto
        title = response.css('#productTitle::text').get()
        if not title:
            title = response.css('#title::text').get()
        if not title:
            title = response.css('.product-title-word-break::text').get()
        if not title:
            title = response.css('title::text').get()

        if title:
            title = title.strip()

        if not title:
            self.logger.warning(f"Não foi possível extrair o título do produto: {response.url}")
            title = "Título não encontrado"

        price = None
        old_price = None

        # --- Extração de Preço Atual ---
        price_symbol_new = response.css('span.priceToPay span.a-price-symbol::text').get()
        price_whole_new = response.css('span.priceToPay span.a-price-whole::text').get()
        price_fraction_new = response.css('span.priceToPay span.a-price-fraction::text').get()

        if price_symbol_new and price_whole_new:
            price = f"{price_symbol_new.strip()}{price_whole_new.strip()}"
            if price_fraction_new:
                price += f",{price_fraction_new.strip()}"
            self.logger.info(f"Preço atual (priceToPay) encontrado: {price}")

        # Tentativa alternativa para preço atual
        if not price:
            offscreen_price_text = response.css('div.aok-relative > span.aok-offscreen::text').get()
            if offscreen_price_text:
                match = re.search(r"(R\$\s*[\d.,]+)", offscreen_price_text)
                if match:
                    price = match.group(1).replace(" ", "").replace(".", "").replace(",", ",")
                    price_parts = price.replace("R$", "").split(',')
                    if len(price_parts) == 2:
                        price = f"R$ {price_parts[0]},{price_parts[1]}"
                    elif len(price_parts) == 1:
                         price = f"R$ {price_parts[0]},00"
                    else:
                        price = None

        # --- Extração de Preço Antigo ---
        old_price_offscreen_de = response.xpath('//div[contains(@id, "corePriceDisplay_desktop_feature_div")]//span[contains(@class, "aok-offscreen") and contains(text(), "De:")]/text()').get()
        if old_price_offscreen_de:
            match = re.search(r"R\$\s*([\d.,]+)", old_price_offscreen_de)
            if match:
                old_price = f"R${match.group(1).replace('.', ',')}"
                self.logger.info(f"Preço antigo (aok-offscreen 'De:') encontrado: {old_price}")

        # Extrai a imagem
        image_url = response.css('#landingImage::attr(src)').get()
        if not image_url:
            image_url = response.css('#imgBlkFront::attr(src)').get()
        if not image_url:
            image_url = response.css('#main-image::attr(src)').get()
        if not image_url:
            image_url = response.css('.a-dynamic-image::attr(src)').get()
        if not image_url:
            image_url = response.css('#landingImage::attr(data-old-hires)').get()
        if not image_url:
            data_dynamic = response.css('#landingImage::attr(data-a-dynamic-image)').get()
            if data_dynamic:
                try:
                    import json
                    image_dict = json.loads(data_dynamic)
                    if image_dict and isinstance(image_dict, dict) and len(image_dict) > 0:
                        image_url = list(image_dict.keys())[0]
                except Exception as e:
                    self.logger.error(f"Erro ao extrair imagem do data-a-dynamic-image: {e}")
        if not image_url:
            image_url = response.css('img[src*="images-amazon"]::attr(src)').get()

        # Extrai informações de parcelamento
        installments = response.css('#installmentCalculator::text').get()
        if not installments:
            installments = "Consulte parcelas no site"

        # Extrai informações de cupom
        coupon_info = ""

        # Extrai informações de frete
        shipping = "Verificar frete"
        shipping_text = response.css('#deliveryBlockMessage::text').get()
        if shipping_text:
            shipping_text = shipping_text.lower()
            if "frete grátis" in shipping_text or "entrega grátis" in shipping_text:
                shipping = "Com frete"

        prime_eligible = response.css('.prime-eligible')
        if prime_eligible:
            shipping = "Com frete (Prime)"

        # Extrai o ID do produto da URL (ASIN)
        product_id = None
        patterns = [
            r"/dp/([A-Z0-9]{10})",
            r"/gp/product/([A-Z0-9]{10})",
            r"/dp%2F([A-Z0-9]{10})",
            r"&ASIN=([A-Z0-9]{10})",
            r"/ASIN/([A-Z0-9]{10})",
        ]
        
        for pattern in patterns:
            match = re.search(pattern, response.url, re.IGNORECASE)
            if match:
                product_id = match.group(1).upper()
                break
        
        # Limpa a URL do produto
        product_url = self.clean_product_url(response.url)

        # Retorna os dados do produto
        return {
            "platform": "Amazon",
            "product_id": product_id,
            "url_produto": product_url,
            "url_afiliado": "",
            "title": title,
            "description": "",
            "price": price if price else "Preço não disponível",
            "old_price": old_price if old_price else None,
            "image_url": image_url,
            "installments": installments.strip() if installments else "Consulte parcelas no site",
            "coupon_info": coupon_info,
            "shipping": shipping.strip() if shipping else "Verificar frete",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            # Campos adicionais para compatibilidade com PromoHunter
            "ativo": True,
            "menor_preco": False,
            "indicamos": False,
            "disparar_whatsapp": False,
            "frete": False,
            "invalidProduct": False,
            "isStory": False,
            "cupom": coupon_info
        }

    def clean_product_url(self, url):
        """
        Limpa a URL do produto, removendo parâmetros de tracking
        """
        if not url:
            return ""

        try:
            parsed = urlparse(url)

            # Remove fragmentos (tudo após #)
            clean_url = url.split('#')[0]

            # Se for uma URL de produto da Amazon, simplifica para a forma canônica
            if "/dp/" in parsed.path:
                asin_match = re.search(r"/dp/([A-Z0-9]{10})", parsed.path, re.IGNORECASE)
                if asin_match:
                    asin = asin_match.group(1)
                    return f"https://{parsed.netloc}/dp/{asin}"
            
            return clean_url
        except Exception as e:
            self.logger.error(f"Erro ao limpar URL do produto: {e}")
            return url
