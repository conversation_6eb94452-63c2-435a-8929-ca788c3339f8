import requests
from scrapy import signals
from urllib.parse import urlencode
import json
import random

class ScrapeOpsFakeHeadersMiddleware:
    @classmethod
    def from_crawler(cls, crawler):
        return cls(crawler.settings)

    def __init__(self, settings):
        self.scrapeops_api_key = settings.get('SCRAPEOPS_API_KEY')
        self.scrapeops_endpoint = 'https://headers.scrapeops.io/v1/browser-headers'
        self.scrapeops_fake_headers_active = settings.get('SCRAPEOPS_FAKE_HEADERS_ENABLED', True)
        self.headers_list = []
        self._get_headers_list()
        self.retry_counter = 0
        self.max_retries = 3

    def _get_headers_list(self):
        """Get list of headers from ScrapeOps API"""
        try:
            payload = {'api_key': self.scrapeops_api_key}
            response = requests.get(
                self.scrapeops_endpoint,
                params=payload,
                timeout=30
            )
            json_response = response.json()
            self.headers_list = json_response.get('result', [])
        except Exception as e:
            print(f'Error fetching headers: {e}')
            if self.retry_counter < self.max_retries:
                self.retry_counter += 1
                self._get_headers_list()

    def _get_random_header(self):
        if not self.headers_list:
            return None
        return random.choice(self.headers_list)

    def process_request(self, request, spider):
        if self.scrapeops_fake_headers_active:
            random_header = self._get_random_header()
            if random_header:
                for key, value in random_header.items():
                    request.headers[key] = value