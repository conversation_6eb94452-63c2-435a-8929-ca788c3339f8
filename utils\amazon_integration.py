import os
import sys
import json
import logging
import asyncio
from datetime import datetime
import csv # Importar módulo csv
import re # Importar módulo re

# Adiciona o diretório raiz ao PYTHONPATH
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.scrapers.amazon_scraper import AmazonScraper
from src.scrapers.amazon_selenium import AmazonSeleniumScraper
from utils.store_manager import StoreManager
from utils.ai_manager import AIManager

logger = logging.getLogger(__name__)

class AmazonIntegration:
    """Classe para integrar o scraper da Amazon ao sistema Promohunter"""

    def __init__(self, store_manager=None, ai_manager=None):
        self.store_manager = store_manager if store_manager else StoreManager()
        self.ai_manager = ai_manager if ai_manager else AIManager()
        self.scraper = AmazonScraper()
        self.scraper.ai_manager = self.ai_manager  # Passa o ai_manager para o scraper
        self.output_dir = os.path.join(project_root, "output")
        self._is_running = False
        self._selenium_scraper = None
        os.makedirs(self.output_dir, exist_ok=True)

        # Log para verificar se o AI Manager está sendo passado corretamente
        logger.info(f"AmazonIntegration inicializada com AI Manager: {type(self.ai_manager)}")
        logger.info(f"API atual do AI Manager: {self.ai_manager.current_api}")

        # Renomear o botão de volta para "Executar Scraper" se necessário
        # Esta lógica deve ser implementada na interface gráfica

    async def run_scraper(self, max_pages=3, only_discount=False, log_callback=None):
        """Executa o scraper da Amazon e retorna os produtos encontrados"""
        self._is_running = True  # Marcar como rodando

        info_msg = f"Iniciando scraper da Amazon (apenas com desconto: {only_discount})"
        logger.info(info_msg)
        if log_callback:
            log_callback(info_msg)
            # Passar o log_callback para o scraper
            self.scraper.log_callback = log_callback

        try:
            # Configura a loja atual para Amazon
            self.store_manager.set_current_store("Amazon")

            # Tentar primeiro com scraper tradicional
            self.scraper.log_callback = log_callback
            products = await self.scraper.run(max_pages=max_pages, only_discount=only_discount)
            amazon_category_name = "Amazon_Produtos"  # Categoria padrão para scraper tradicional

            # Se não encontrou produtos, tentar com Selenium
            if not products:
                info_msg = "Scraper tradicional não encontrou produtos. Tentando com Selenium..."
                logger.info(info_msg)
                if log_callback:
                    log_callback(info_msg)

                products, amazon_category_name = await self._run_selenium_scraper(max_pages, only_discount, log_callback)

            # Converter formato do scraper tradicional para formato padrão
            if products and not products[0].get('plataforma'):  # Se não tem 'plataforma', é do scraper tradicional
                converted_products = []
                for product in products:
                    converted_product = {
                        "plataforma": "Amazon",
                        "url_produto": product.get('link', ''),
                        "url_afiliado": product.get('link', ''),
                        "url_imagem": product.get('image_url', ''),
                        "titulo": product.get('title', ''),
                        "categoria": product.get('category', 'Melhor Amigo'),
                        "categoria_id": "",
                        "subcategoria": "",
                        "subcategoria_id": "",
                        "descricao": "",
                        "preco_atual": product.get('price', 0),
                        "preco_antigo": product.get('old_price', 0),
                        "preco_alternativo": "",
                        "ativo": "TRUE",
                        "cupom": "",
                        "menor_preco": "FALSE",
                        "indicamos": "FALSE",
                        "disparar_whatsapp": "FALSE",
                        "grupo_whatsapp": "",
                        "frete": "",
                        "invalidProduct": "FALSE",
                        "isStory": "FALSE",
                        "image_id": "",
                        # Campos para compatibilidade interna
                        "price": product.get('price', 0),
                        "old_price": product.get('old_price', 0),
                        "discount": product.get('discount', 0),
                        "link": product.get('link', ''),
                        "image_url": product.get('image_url', ''),
                        "store": "Amazon",
                        "is_deal": product.get('is_deal', False)
                    }
                    converted_products.append(converted_product)
                products = converted_products

            # Aplicar IA para categorização respeitando a API selecionada
            if self.ai_manager and products:
                info_msg = f"Aplicando IA para categorização de {len(products)} produtos..."
                logger.info(info_msg)
                if log_callback:
                    log_callback(info_msg)

                for product in products:
                    try:
                        title = product.get('titulo', '') or product.get('title', '')
                        if title:
                            # Usar o método correto baseado na API atual
                            if self.ai_manager.current_api == "GoogleAI":
                                category, category_id = await self.ai_manager.get_category_googleAI(title)
                                subcategory, subcategory_id = await self.ai_manager.get_subcategory_googleAI(title, category)
                            elif self.ai_manager.current_api == "OpenAI":
                                category, category_id = await self.ai_manager.get_category_openAI(title)
                                subcategory, subcategory_id = await self.ai_manager.get_subcategory_openAI(title, category)
                            else:
                                category = await self.ai_manager.get_category_openRouter(title)
                                subcategory = await self.ai_manager.get_subcategory_openRouter(title, category)
                                # Encontrar IDs
                                category_id = next((int(k) for k, v in self.ai_manager.categories.items() if v['name'] == category), 15)
                                subcategory_id = 1

                            # Atualizar produto com dados da IA
                            product['categoria'] = category
                            product['categoria_id'] = category_id
                            product['subcategoria'] = subcategory
                            product['subcategoria_id'] = subcategory_id

                    except Exception as e:
                        logger.error(f"Erro ao aplicar IA no produto {title}: {e}")
                        # Manter categoria padrão se IA falhar
                        product['categoria'] = 'Melhor Amigo'
                        product['categoria_id'] = 15
                        product['subcategoria'] = 'Mais produtos relacionados'
                        product['subcategoria_id'] = 11

            # Processa e salva os produtos em arquivos CSV por categoria
            info_msg = f"Processando {len(products)} produtos para salvar em CSV"
            logger.info(info_msg)
            if log_callback:
                log_callback(info_msg)

            # Agrupar produtos por categoria da Amazon (busca)
            products_by_category = {amazon_category_name: products}

            # Cabeçalho do CSV baseado no modelo Product (manter consistência com scraper base)
            headers_csv = [
                "plataforma", "url_produto", "url_afiliado", "url_imagem", "titulo",
                "categoria", "categoria_id", "subcategoria", "subcategoria_id", "descricao", "preco_atual",
                "preco_antigo", "preco_alternativo", "ativo", "cupom",
                "menor_preco", "indicamos", "disparar_whatsapp",
                "grupo_whatsapp", "frete", "invalidProduct", "isStory", "image_id"
            ]

            output_paths = {}
            for category, category_products in products_by_category.items():
                # Limpar nome da categoria para usar no nome do arquivo
                safe_category_name = re.sub(r'[^\w\s-]', '', category).replace(' ', '_')
                output_file = os.path.join(self.output_dir, f"{safe_category_name}.csv")
                output_paths[category] = output_file

                info_msg = f"Salvando {len(category_products)} produtos para a categoria '{category}' em {output_file}"
                logger.info(info_msg)
                if log_callback:
                    log_callback(info_msg)

                # Abrir arquivo CSV em modo de adição ('a') para não sobrescrever se a categoria já existir
                # Usar 'w' na primeira vez para escrever o cabeçalho, ou verificar se o arquivo existe

                # Verificar se o arquivo existe para decidir se escreve o cabeçalho
                write_header = not os.path.exists(output_file)

                with open(output_file, 'a', encoding='utf-8-sig', newline='') as f:
                    writer = csv.DictWriter(f, fieldnames=headers_csv, extrasaction='ignore')

                    if write_header:
                        writer.writeheader()
                        logger.debug(f"Cabeçalho escrito para {output_file}")

                    # Escrever os produtos
                    for product_data in category_products:
                        # Garantir que todos os campos do cabeçalho estão no dicionário do produto
                        # e converter para string, lidando com None
                        row_data = {h: str(product_data.get(h, '')) if product_data.get(h) is not None else '' for h in headers_csv}
                        writer.writerow(row_data)

                info_msg = f"Produtos da categoria '{category}' salvos com sucesso."
                logger.info(info_msg)
                if log_callback:
                    log_callback(info_msg)

            info_msg = f"Processamento da Amazon concluído. Total de produtos encontrados: {len(products)}"
            logger.info(info_msg)
            if log_callback:
                log_callback(info_msg)

            return products, output_paths # Retorna a lista completa de produtos e os caminhos dos arquivos CSV

        except Exception as e:
            error_msg = f"Erro ao executar scraper da Amazon: {e}"
            logger.error(error_msg)
            if log_callback:
                log_callback(error_msg)
            return [], None
        finally:
            self._is_running = False  # Marcar como parado ao final

    async def _run_selenium_scraper(self, max_pages, only_discount, log_callback):
        """Executa o scraper Selenium da Amazon"""
        try:
            # Verificar se foi solicitada a parada
            if not self._is_running:
                return [], "Amazon_Parado"

            selenium_scraper = AmazonSeleniumScraper(headless=True)
            self._selenium_scraper = selenium_scraper  # Armazenar referência para poder parar

            # Carregar categorias do categories.json
            categories_file = os.path.join(project_root, "categories.json")
            amazon_categories = {}

            if os.path.exists(categories_file):
                try:
                    with open(categories_file, 'r', encoding='utf-8') as f:
                        categories_data = json.load(f)

                    # Extrair categorias Amazon
                    for category_data in categories_data.get("Amazon", []):
                        if category_data.get("url_template"):
                            amazon_categories[category_data["url_template"]] = category_data["name"]

                except Exception as e:
                    logger.error(f"Erro ao carregar categories.json: {e}")

            # Se não encontrou categorias no arquivo, usar URLs padrão
            if not amazon_categories:
                amazon_categories = {
                    "https://www.amazon.com.br/s?k=ração+cachorro": "Racao_Cachorro",
                    "https://www.amazon.com.br/s?k=brinquedo+pet": "Brinquedo_Pet",
                    "https://www.amazon.com.br/s?k=coleira+cachorro": "Coleira_Cachorro",
                    "https://www.amazon.com.br/s?k=ração+gato": "Racao_Gato",
                    "https://www.amazon.com.br/s?k=petisco+cachorro": "Petisco_Cachorro"
                }

            # Escolher uma URL aleatoriamente
            import random
            url = random.choice(list(amazon_categories.keys()))
            amazon_category_name = amazon_categories[url]

            info_msg = f"Iniciando scraper Selenium da Amazon para categoria: {amazon_category_name}..."
            logger.info(info_msg)
            if log_callback:
                log_callback(info_msg)

            # Verificar se foi solicitada a parada antes do scraping
            if not self._is_running:
                selenium_scraper.close()
                return [], "Amazon_Parado"

            # Scraping com Selenium
            selenium_products = selenium_scraper.scrape_amazon_page(url, max_products=60 * max_pages)

            # Verificar se foi solicitada a parada após o scraping
            if not self._is_running:
                selenium_scraper.close()
                return [], "Amazon_Parado"

            # Filtrar produtos se necessário
            if only_discount:
                selenium_products = [p for p in selenium_products if p.get('preco_antigo', 0) > 0]

            # Converter formato do Selenium para formato do sistema
            products = []
            for product in selenium_products:
                converted_product = {
                    "plataforma": "Amazon",
                    "url_produto": product.get('url_produto', ''),
                    "url_afiliado": product.get('url_afiliado', product.get('url_produto', '')),
                    "url_imagem": product.get('url_imagem', ''),
                    "titulo": product.get('titulo', ''),
                    "categoria": product.get('categoria', 'Melhor Amigo'),
                    "categoria_id": "",
                    "subcategoria": "",
                    "subcategoria_id": "",
                    "descricao": "",
                    "preco_atual": product.get('preco_atual', 0),
                    "preco_antigo": product.get('preco_antigo', 0),
                    "preco_alternativo": "",
                    "ativo": "TRUE",
                    "cupom": "",
                    "menor_preco": "FALSE",
                    "indicamos": "FALSE",
                    "disparar_whatsapp": "FALSE",
                    "grupo_whatsapp": "",
                    "frete": "",
                    "invalidProduct": "FALSE",
                    "isStory": "FALSE",
                    "image_id": "",
                    # Campos para compatibilidade interna
                    "price": product.get('preco_atual', 0),
                    "old_price": product.get('preco_antigo', 0),
                    "discount": product.get('desconto', 0),
                    "link": product.get('url_produto', ''),
                    "image_url": product.get('url_imagem', ''),
                    "store": "Amazon",
                    "is_deal": product.get('preco_antigo', 0) > 0
                }
                products.append(converted_product)

            selenium_scraper.close()

            info_msg = f"Selenium encontrou {len(products)} produtos"
            logger.info(info_msg)
            if log_callback:
                log_callback(info_msg)

            return products, amazon_category_name

        except Exception as e:
            error_msg = f"Erro no scraper Selenium: {e}"
            logger.error(error_msg)
            if log_callback:
                log_callback(error_msg)
            return [], "Amazon_Erro"

    def start(self, log_callback=None):
        """Inicia o scraper em uma thread separada"""
        return self.scraper.start(log_callback=log_callback)

    def stop(self):
        """Para o scraper"""
        # Parar o scraper tradicional
        scraper_stopped = self.scraper.stop()

        # Parar o Selenium se estiver rodando
        if hasattr(self, '_selenium_scraper') and self._selenium_scraper:
            try:
                self._selenium_scraper.close()
                self._selenium_scraper = None
            except Exception as e:
                logger.error(f"Erro ao fechar Selenium: {e}")

        # Marcar como parado
        self._is_running = False

        return scraper_stopped

    def get_status(self):
        """Retorna o status atual do scraper"""
        return self.scraper.get_status()

# Função para teste direto do módulo
if __name__ == "__main__":
    # Configuração de logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(os.path.join(project_root, "logs", "amazon_integration.log"))
        ]
    )

    # Executa o scraper
    async def test_integration():
        integration = AmazonIntegration()
        products, output_path = await integration.run_scraper(max_pages=2)
        print(f"Total de produtos encontrados: {len(products)}")
        print(f"Produtos salvos em: {output_path}")

    # Executa o teste
    asyncio.run(test_integration())
