import scrapy
import json
import os
import sys
import logging
from urllib.parse import urljoin, urlparse
import re

# Adiciona o diretório raiz ao PYTHONPATH
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from utils.ai_manager import AIManager
from utils.store_manager import StoreManager

logger = logging.getLogger(__name__)

class MercadoLivreSpider(scrapy.Spider):
    name = 'mercadolivre'
    allowed_domains = ['mercadolivre.com.br']

    custom_settings = {
        'DOWNLOAD_DELAY': 2,
        'RANDOMIZE_DOWNLOAD_DELAY': True,
        'CONCURRENT_REQUESTS': 1,
        'CONCURRENT_REQUESTS_PER_DOMAIN': 1,
        'USER_AGENT': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'DEFAULT_REQUEST_HEADERS': {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        },
        'ROBOTSTXT_OBEY': False,
        'COOKIES_ENABLED': True,
        'AUTOTHROTTLE_ENABLED': True,
        'AUTOTHROTTLE_START_DELAY': 1,
        'AUTOTHROTTLE_MAX_DELAY': 10,
        'AUTOTHROTTLE_TARGET_CONCURRENCY': 1.0,
    }

    def __init__(self, category_info=None, log_callback=None, *args, **kwargs):
        super(MercadoLivreSpider, self).__init__(*args, **kwargs)
        self.category_info = category_info or {}
        self.log_callback = log_callback
        self.ai_manager = AIManager()
        self.store_manager = StoreManager()
        self.products_found = 0
        self.products_processed = 0

        # Configurar logging
        self.logger.setLevel(logging.INFO)

        if self.log_callback:
            self.log_callback("Spider do Mercado Livre inicializada")

    def start_requests(self):
        """Gera as requisições iniciais"""
        if not self.category_info:
            self.logger.error("Informações da categoria não fornecidas")
            return

        url_template = self.category_info.get('url_template', '')
        max_page = self.category_info.get('max_page', 1)

        if not url_template:
            self.logger.error("URL template não encontrada")
            return

        for page in range(1, max_page + 1):
            url = url_template.format(i=page)
            self.logger.info(f"Processando página {page}: {url}")

            if self.log_callback:
                self.log_callback(f"Processando página {page} de {max_page}")

            yield scrapy.Request(
                url=url,
                callback=self.parse,
                meta={'page': page},
                dont_filter=True
            )

    def parse(self, response):
        """Processa a resposta da página"""
        page = response.meta.get('page', 'Unknown')
        self.logger.info(f"Processando página {page} - Status: {response.status}")

        if response.status != 200:
            self.logger.error(f"Erro na página {page}: Status {response.status}")
            return

        # Verificar se o conteúdo é texto
        try:
            content_type = response.headers.get('content-type', b'').decode('utf-8')
            self.logger.info(f"Content-Type: {content_type}")

            # Verificar se conseguimos acessar o texto
            text_content = response.text
            self.logger.info(f"Tamanho do conteúdo: {len(text_content)} caracteres")

        except Exception as e:
            self.logger.error(f"Erro ao acessar conteúdo da resposta: {e}")
            return

        # Seletores para produtos do Mercado Livre
        product_selectors = [
            'div.andes-card.poly-card--grid-card',
            'div.andes-card',
            'div.ui-search-result',
            'li.ui-search-layout__item',
            'div.promotion-item'
        ]

        products = []
        for selector in product_selectors:
            try:
                products = response.css(selector)
                if products:
                    self.logger.info(f"Encontrados {len(products)} produtos com seletor: {selector}")
                    break
            except Exception as e:
                self.logger.error(f"Erro ao usar seletor {selector}: {e}")
                continue

        if not products:
            self.logger.warning(f"Nenhum produto encontrado na página {page}")
            self.logger.info(f"URL: {response.url}")
            self.logger.info(f"Tamanho da resposta: {len(response.text)} caracteres")
            return

        self.products_found += len(products)
        self.logger.info(f"Encontrados {len(products)} produtos na página {page}")

        if self.log_callback:
            self.log_callback(f"Encontrados {len(products)} produtos na página {page}")

        # Processar cada produto
        for idx, product in enumerate(products, 1):
            try:
                product_data = self.extract_product_data(product, response.url)
                if product_data:
                    self.products_processed += 1
                    yield product_data

                    if self.log_callback:
                        self.log_callback(f"Produto {idx} processado: {product_data.get('titulo', 'Sem título')[:50]}...")

            except Exception as e:
                self.logger.error(f"Erro ao processar produto {idx} na página {page}: {e}")

        self.logger.info(f"Página {page} concluída. Total processado: {self.products_processed}")

    def extract_product_data(self, product, base_url):
        """Extrai dados de um produto"""
        try:
            # Extrair título
            title_selectors = [
                'h2.ui-search-item__title::text',
                'h2.promotion-item__title::text',
                'span.ui-search-item__title::text',
                'a.poly-component__title::text',
                'h2::text',
                'h3::text'
            ]

            title = None
            for selector in title_selectors:
                title = product.css(selector).get()
                if title:
                    title = title.strip()
                    break

            if not title:
                self.logger.warning("Título não encontrado")
                return None

            # Extrair preço atual
            price_selectors = [
                'span.andes-money-amount__fraction::text',
                'span.price-tag-amount::text',
                'span.ui-search-price__part::text',
                'span.promotion-item__price::text'
            ]

            price_text = None
            for selector in price_selectors:
                price_text = product.css(selector).get()
                if price_text:
                    break

            price = self.clean_price(price_text) if price_text else 0.0

            # Extrair preço antigo
            old_price_selectors = [
                's.andes-money-amount--previous .andes-money-amount__fraction::text',
                'span.andes-money-amount--previous .andes-money-amount__fraction::text',
                'span.price-tag-amount-previous::text'
            ]

            old_price_text = None
            for selector in old_price_selectors:
                old_price_text = product.css(selector).get()
                if old_price_text:
                    break

            old_price = self.clean_price(old_price_text) if old_price_text else 0.0

            # Extrair link
            link_selectors = [
                'a.ui-search-link::attr(href)',
                'a.promotion-item__link::attr(href)',
                'a.poly-component__title::attr(href)',
                'a::attr(href)'
            ]

            link = None
            for selector in link_selectors:
                link = product.css(selector).get()
                if link:
                    if not link.startswith('http'):
                        link = urljoin(base_url, link)
                    break

            # Extrair imagem
            image_selectors = [
                'img.ui-search-result-image__element::attr(src)',
                'img.promotion-item__img::attr(src)',
                'img::attr(data-src)',
                'img::attr(src)'
            ]

            image_url = None
            for selector in image_selectors:
                image_url = product.css(selector).get()
                if image_url:
                    if image_url.startswith('//'):
                        image_url = 'https:' + image_url
                    break

            # Categoria padrão (IA será aplicada posteriormente)
            category = "Brinquedos"

            # Calcular desconto
            discount = 0
            if old_price > 0 and price > 0:
                discount = round(100 - (price * 100 / old_price))

            return {
                'plataforma': 'MercadoLivre',
                'titulo': title,
                'preco_atual': price,
                'preco_antigo': old_price,
                'desconto': discount,
                'url_produto': link,
                'url_afiliado': link,  # Para ML, URL afiliado é a mesma
                'url_imagem': image_url,
                'categoria': category,
                'ativo': True
            }

        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None

    def clean_price(self, price_text):
        """Limpa e converte texto de preço para float"""
        if not price_text:
            return 0.0

        try:
            # Remover caracteres não numéricos exceto vírgula
            price_clean = re.sub(r'[^\d,]', '', price_text)
            # Substituir vírgula por ponto
            price_clean = price_clean.replace(',', '.')
            return float(price_clean) if price_clean else 0.0
        except Exception:
            return 0.0

    def closed(self, reason):
        """Chamado quando o spider termina"""
        self.logger.info(f"Spider finalizada. Motivo: {reason}")
        self.logger.info(f"Total de produtos encontrados: {self.products_found}")
        self.logger.info(f"Total de produtos processados: {self.products_processed}")

        if self.log_callback:
            self.log_callback(f"Scraping concluído: {self.products_processed} produtos processados")
