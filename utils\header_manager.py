import os
import requests
from random import randint
import logging

class HeaderManager:
    def __init__(self):
        self.api_key = os.getenv("SCRAPE_OPS_API_KEY")
        if not self.api_key:
            logging.warning("SCRAPE_OPS_API_KEY não encontrada nas variáveis de ambiente")
        self.headers_list = self.get_headers_list()
        self._current_header = None

    def get_headers_list(self):
        if not self.api_key:
            logging.warning("Sem API key para ScrapeOps, usando headers padrão")
            return []
            
        try:
            response = requests.get(
                url='https://headers.scrapeops.io/v1/browser-headers',
                params={
                    'api_key': self.api_key,
                    'num_results': '50'
                },
                timeout=10
            )
            response.raise_for_status()
            json_response = response.json()
            headers = json_response.get("result", [])
            logging.info(f"Obtidos {len(headers)} headers do ScrapeOps")
            return headers
        except Exception as e:
            logging.error(f"Erro ao obter headers do ScrapeOps: {e}")
            return []

    def get_random_header(self):
        if not self.headers_list:
            logging.debug("Usando header padrão")
            return {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept-Language": "pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }
        
        self._current_header = self.headers_list[randint(0, len(self.headers_list) - 1)]
        logging.debug("Usando header aleatório do ScrapeOps")
        return self._current_header

