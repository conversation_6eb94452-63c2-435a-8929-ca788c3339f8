{"MercadoLivre": [{"id": 1, "name": "Brinquedos", "url_template": "https://www.mercadolivre.com.br/ofertas?container_id=MLB787147-1&page={i}", "max_page": 1}], "Magalu": [{"id": 1, "name": "<PERSON><PERSON>", "url_template": "https://www.magazinevoce.com.br/magazinepromobelloficial/livros/l/li/delivery_magalu---magalu_indica/?page={i}", "max_page": 1}], "Amazon": [{"id": 1, "name": "Pet Shop", "url_template": "https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-pet-products&promotionsSearchLastSeenAsin=B0DV1H1N6Q&promotionsSearchStartIndex={i}&promotionsSearchPageSize=60", "max_page": 1}]}