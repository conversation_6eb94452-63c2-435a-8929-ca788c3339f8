{"MercadoLivre": [{"id": 1, "name": "Brinquedos", "url_template": "https://www.mercadolivre.com.br/ofertas?container_id=MLB787147-1&domain_id=MLB-DOLLS$MLB-ACTION_FIGURES$MLB-TOYS_AND_GAMES$MLB-DIECAST_VEHICLES$MLB-TOY_BUILDING_SETS$MLB-STUFFED_TOYS$MLB-BOARD_GAMES$MLB-KICK_SCOOTERS$MLB-COSTUMES$MLB-TRAMPOLINES$MLB-INFLATABLE_POOLS$MLB-REMOTE_CONTROL_TOY_VEHICLES$MLB-KIDS_TENTS$MLB-PUZZLES$MLB-POWERED_RIDE_ON_TOYS$MLB-KITCHEN_PLAYSETS$MLB-TRADING_CARD_GAMES$MLB-DOLLHOUSES$MLB-ELECTRIC_SCOOTERS$MLB-PUSH_AND_RIDING_TOYS$MLB-SPINNING_TOPS$MLB-KIDS_TRICYCLES$MLB-DOLL_AND_ACTION_FIGURE_SETS$MLB-BABY_SUPPLIES$MLB-CAR_RACE_TRACKS_AND_BLAST_LAUNCHERS&page={i}", "max_page": 1}], "Magalu": [{"id": 1, "name": "<PERSON><PERSON>", "url_template": "https://www.magazinevoce.com.br/magazinepromobelloficial/livros/l/li/delivery_magalu---magalu_indica/?page={i}", "max_page": 1}], "Amazon": [{"id": 1, "name": "Pet Shop", "url_template": "https://www.amazon.com.br/deals?ref_=nav_cs_gb&bubble-id=deals-collection-pet-products&promotionsSearchLastSeenAsin=B0DV1H1N6Q&promotionsSearchStartIndex={i}&promotionsSearchPageSize=60", "max_page": 1}]}