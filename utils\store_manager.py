import json
import os
import logging

logger = logging.getLogger(__name__)

class StoreManager:
    """
    <PERSON><PERSON><PERSON> as diferentes lojas (marketplaces) disponíveis no sistema.
    """
    def __init__(self):
        self.stores = {
            "MercadoLivre": {
                "name": "Mercado Livre",
                "id": "mercadolivre",
                "url_base": "https://www.mercadolivre.com.br/",
                "category_format": "https://www.mercadolivre.com.br/ofertas?container_id={container_id}&domain_id={domain_id}",
                "page_format": "https://www.mercadolivre.com.br/ofertas?container_id={container_id}&domain_id={domain_id}&page={i}"
            },
            "Magalu": {
                "name": "Magazine Luiza",
                "id": "magalu",
                "url_base": "https://www.magazinevoce.com.br/magazinepromobelloficial/",
                "category_format": "https://www.magazinevoce.com.br/magazinepromobelloficial/{category}/l/{category_id}/",
                "page_format": "https://www.magazinevoce.com.br/magazinepromobelloficial/{category}/l/{category_id}/?page={i}"
            },
            "Amazon": {
                "name": "Amazon Brasil",
                "id": "amazon",
                "url_base": "https://www.amazon.com.br/",
                "category_format": "https://www.amazon.com.br/s?k={category}&i=deals",
                "page_format": "https://www.amazon.com.br/s?k={category}&i=deals&page={i}"
            }
        }
        self.current_store = "MercadoLivre"
        self.load_config()
    
    def load_config(self):
        """Carrega a configuração de loja salva, se existir"""
        config_file = "store_config.json"
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    if 'current_store' in config and config['current_store'] in self.stores:
                        self.current_store = config['current_store']
                        logger.info(f"Loja carregada: {self.current_store}")
        except Exception as e:
            logger.error(f"Erro ao carregar configuração de loja: {e}")
    
    def save_config(self):
        """Salva a configuração atual de loja"""
        config_file = "store_config.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump({'current_store': self.current_store}, f, ensure_ascii=False, indent=4)
                logger.info(f"Configuração de loja salva: {self.current_store}")
        except Exception as e:
            logger.error(f"Erro ao salvar configuração de loja: {e}")
    
    def get_store_names(self):
        """Retorna a lista de nomes de lojas disponíveis"""
        return list(self.stores.keys())
    
    def get_current_store(self):
        """Retorna a loja atual"""
        return self.current_store
    
    def get_store_info(self, store_name=None):
        """Retorna informações da loja especificada ou da loja atual"""
        store = store_name or self.current_store
        return self.stores.get(store, self.stores["MercadoLivre"])
    
    def set_current_store(self, store_name):
        """Define a loja atual"""
        if store_name in self.stores:
            self.current_store = store_name
            self.save_config()
            logger.info(f"Loja atual alterada para: {store_name}")
            return True
        return False
